apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-test-tenant-backend
  namespace: tenant-test-tenant
spec:
  template:
    spec:
      initContainers:
      - name: init-storage
        image: busybox
        command: ['sh', '-c', 'mkdir -p /storage/clear && chmod 777 /storage/clear']
        volumeMounts:
        - name: storage-volume
          mountPath: /storage
      containers:
      - name: backend
        volumeMounts:
        - name: storage-volume
          mountPath: /storage
      volumes:
      - name: storage-volume
        emptyDir: {}
