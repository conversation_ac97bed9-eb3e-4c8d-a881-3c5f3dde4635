apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: tenant-test-tenant-backend
    component: backend
    tenant: test-tenant
  name: tenant-test-tenant-backend
  namespace: tenant-test-tenant
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-test-tenant-backend
  template:
    metadata:
      labels:
        app: tenant-test-tenant-backend
        component: backend
        tenant: test-tenant
        tenant.architrave.io/tenant-id: test-tenant
    spec:
      containers:
      - env:
        - name: TENANT_ID
          value: test-tenant
        - name: TENANT_NAME
          value: Test Tenant
        - name: ENVIRONMENT
          value: production
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              key: host
              name: db-credentials
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              key: port
              name: db-credentials
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              key: database
              name: db-credentials
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              key: username
              name: db-credentials
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: db-credentials
        - name: RABBITMQ_HOST
          value: tenant-test-tenant-rabbitmq
        - name: RABBITMQ_PORT
          value: "5672"
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
        name: backend
        ports:
        - containerPort: 9000
          name: http
        resources:
          limits:
            cpu: 100m
            memory: 256Mi
          requests:
            cpu: 50m
            memory: 128Mi
        volumeMounts:
        - mountPath: /storage
          name: storage-volume
        - mountPath: /usr/local/etc/php-fpm.d/www.conf
          name: php-fpm-config
          subPath: www.conf
        - mountPath: /storage/ArchAssets/config/autoload/local.php
          name: app-config
          subPath: local.php
      initContainers:
      - command:
        - sh
        - -c
        - |
          # Create proper directory structure for the application
          mkdir -p /storage/clear && chmod 777 /storage/clear &&
          mkdir -p /storage/ArchAssets/config/autoload && chmod 777 /storage/ArchAssets/config/autoload &&
          mkdir -p /storage/ArchAssets/public && chmod 777 /storage/ArchAssets/public &&
          mkdir -p /storage/ArchAssets/vendor/bin && chmod 777 /storage/ArchAssets/vendor/bin &&
          # Create dummy doctrine-module file to prevent errors
          echo '#!/bin/sh' > /storage/ArchAssets/vendor/bin/doctrine-module &&
          echo 'echo "Dummy doctrine-module"' >> /storage/ArchAssets/vendor/bin/doctrine-module &&
          chmod +x /storage/ArchAssets/vendor/bin/doctrine-module
        image: busybox
        name: init-storage
        resources:
          limits:
            cpu: 50m
            memory: 128Mi
          requests:
            cpu: 25m
            memory: 64Mi
        volumeMounts:
        - mountPath: /storage
          name: storage-volume
      serviceAccountName: tenant-test-tenant-s3-sa
      volumes:
      - emptyDir: {}
        name: storage-volume
      - configMap:
          name: php-fpm-config
        name: php-fpm-config
      - configMap:
          name: app-config
        name: app-config
