apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"tenant-check-testnew-frontend","component":"frontend","tenant":"check-testnew"},"name":"tenant-check-testnew-frontend","namespace":"tenant-check-testnew"},"spec":{"replicas":2,"selector":{"matchLabels":{"app":"tenant-check-testnew-frontend"}},"template":{"metadata":{"labels":{"app":"tenant-check-testnew-frontend","component":"frontend","tenant":"check-testnew","tenant.architrave.io/tenant-id":"check-testnew"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"check-testnew"},{"name":"TENANT_NAME","value":"Check Test New"},{"name":"ENVIRONMENT","value":"production"},{"name":"BACKEND_HOST","value":"webapp"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.4-ssl-test-architrave-assets-com","name":"frontend","ports":[{"containerPort":80},{"containerPort":443}],"resources":{"limits":{"cpu":"200m","memory":"256Mi"},"requests":{"cpu":"100m","memory":"128Mi"}}}]}}}}
  creationTimestamp: "2025-05-16T14:32:49Z"
  generation: 1
  labels:
    app: tenant-check-testnew-frontend
    component: frontend
    tenant: check-testnew
  name: tenant-check-testnew-frontend
  namespace: tenant-check-testnew
  resourceVersion: "818481"
  uid: f9ee03e6-18be-454a-afe3-b82c9dd5f544
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: tenant-check-testnew-frontend
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: tenant-check-testnew-frontend
        component: frontend
        tenant: check-testnew
        tenant.architrave.io/tenant-id: check-testnew
    spec:
      containers:
      - env:
        - name: TENANT_ID
          value: check-testnew
        - name: TENANT_NAME
          value: Check Test New
        - name: ENVIRONMENT
          value: production
        - name: BACKEND_HOST
          value: webapp
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.4-ssl-test-architrave-assets-com
        imagePullPolicy: IfNotPresent
        name: frontend
        ports:
        - containerPort: 80
          protocol: TCP
        - containerPort: 443
          protocol: TCP
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      terminationGracePeriodSeconds: 30
status:
  availableReplicas: 2
  conditions:
  - lastTransitionTime: "2025-05-16T14:33:09Z"
    lastUpdateTime: "2025-05-16T14:33:09Z"
    message: Deployment has minimum availability.
    reason: MinimumReplicasAvailable
    status: "True"
    type: Available
  - lastTransitionTime: "2025-05-16T14:32:49Z"
    lastUpdateTime: "2025-05-16T14:33:09Z"
    message: ReplicaSet "tenant-check-testnew-frontend-85df444f65" has successfully
      progressed.
    reason: NewReplicaSetAvailable
    status: "True"
    type: Progressing
  observedGeneration: 1
  readyReplicas: 2
  replicas: 2
  updatedReplicas: 2
