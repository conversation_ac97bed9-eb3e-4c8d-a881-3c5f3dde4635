apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-check-test-backend
  namespace: tenant-check-test
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tenant-check-test-backend
  template:
    metadata:
      labels:
        app: tenant-check-test-backend
        tenant: check-test
        tenant.architrave.io/tenant-id: check-test
        component: backend
    spec:
      serviceAccountName: tenant-check-test-s3-sa
      initContainers:
      - name: init-storage
        image: busybox
        command:
        - /bin/sh
        - -c
        - |
          mkdir -p /storage/ArchAssets/public
          mkdir -p /storage/ArchAssets/private
          mkdir -p /storage/ArchAssets/logs
          mkdir -p /storage/ArchAssets/cache
          mkdir -p /storage/ArchAssets/config
          mkdir -p /storage/ArchAssets/data
          chmod -R 777 /storage
        volumeMounts:
        - name: storage-volume
          mountPath: /storage
      containers:
      - name: backend
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
        ports:
        - containerPort: 8080
        - containerPort: 9090
        env:
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: host
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: port
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: database
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: password
        - name: S3_BUCKET
          value: "tenant-check-test-assets"
        - name: S3_REGION
          value: "eu-central-1"
        - name: TENANT_ID
          value: "check_test"
        - name: ENVIRONMENT
          value: "production"
        - name: RABBITMQ_HOST
          value: "tenant-check-test-rabbitmq"
        - name: RABBITMQ_PORT
          value: "5672"
        - name: MOCK_DB
          value: "true"
        volumeMounts:
        - name: storage-volume
          mountPath: /storage
        - name: app-config
          mountPath: /storage/ArchAssets/config/local.php
          subPath: local.php
        resources:
          limits:
            cpu: "500m"
            memory: "1Gi"
          requests:
            cpu: "250m"
            memory: "512Mi"
      volumes:
      - name: storage-volume
        emptyDir: {}
      - name: app-config
        configMap:
          name: mock-db-config
