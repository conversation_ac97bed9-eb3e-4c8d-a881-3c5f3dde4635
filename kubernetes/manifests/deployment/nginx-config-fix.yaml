apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: tenant-test-tenant
data:
  default.conf: |
    server {
        listen 80;
        server_name test-tenant.architrave-assets.com;
        
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location /api {
            proxy_pass http://webapp:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
