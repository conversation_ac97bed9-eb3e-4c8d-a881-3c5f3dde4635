apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"tenant-check-testnew-rabbitmq","component":"rabbitmq","tenant":"check-testnew"},"name":"tenant-check-testnew-rabbitmq","namespace":"tenant-check-testnew"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"tenant-check-testnew-rabbitmq"}},"template":{"metadata":{"labels":{"app":"tenant-check-testnew-rabbitmq","component":"rabbitmq","tenant":"check-testnew","tenant.architrave.io/tenant-id":"check-testnew"}},"spec":{"containers":[{"env":[{"name":"RABBITMQ_DEFAULT_USER","value":"tenant_check-testnew"},{"name":"RABBITMQ_DEFAULT_PASS","valueFrom":{"secretKeyRef":{"key":"password","name":"db-credentials"}}}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02","name":"rabbitmq","ports":[{"containerPort":5672},{"containerPort":15672}],"resources":{"limits":{"cpu":"200m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"256Mi"}}}]}}}}
  creationTimestamp: "2025-05-16T14:32:52Z"
  generation: 1
  labels:
    app: tenant-check-testnew-rabbitmq
    component: rabbitmq
    tenant: check-testnew
  name: tenant-check-testnew-rabbitmq
  namespace: tenant-check-testnew
  resourceVersion: "820066"
  uid: 405872b6-fa0e-4f41-8a7c-06a2bfcdf21c
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: tenant-check-testnew-rabbitmq
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: tenant-check-testnew-rabbitmq
        component: rabbitmq
        tenant: check-testnew
        tenant.architrave.io/tenant-id: check-testnew
    spec:
      containers:
      - env:
        - name: RABBITMQ_DEFAULT_USER
          value: tenant_check-testnew
        - name: RABBITMQ_DEFAULT_PASS
          valueFrom:
            secretKeyRef:
              key: password
              name: db-credentials
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
        imagePullPolicy: IfNotPresent
        name: rabbitmq
        ports:
        - containerPort: 5672
          protocol: TCP
        - containerPort: 15672
          protocol: TCP
        resources:
          limits:
            cpu: 200m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 256Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      terminationGracePeriodSeconds: 30
status:
  availableReplicas: 1
  conditions:
  - lastTransitionTime: "2025-05-16T14:40:47Z"
    lastUpdateTime: "2025-05-16T14:40:47Z"
    message: Deployment has minimum availability.
    reason: MinimumReplicasAvailable
    status: "True"
    type: Available
  - lastTransitionTime: "2025-05-16T14:32:52Z"
    lastUpdateTime: "2025-05-16T14:40:47Z"
    message: ReplicaSet "tenant-check-testnew-rabbitmq-b7b885ff9" has successfully
      progressed.
    reason: NewReplicaSetAvailable
    status: "True"
    type: Progressing
  observedGeneration: 1
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1
