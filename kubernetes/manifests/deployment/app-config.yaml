apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: tenant-test-tenant
data:
  local.php: |
    <?php
    return [
      'db' => [
        'driver' => 'pdo_mysql',
        'host' => getenv('DB_HOST'),
        'port' => getenv('DB_PORT'),
        'dbname' => getenv('DB_NAME'),
        'database' => getenv('DB_NAME'),
        'user' => getenv('DB_USER'),
        'username' => getenv('DB_USER'), // Use DB_USER instead of DB_USERNAME
        'password' => getenv('DB_PASSWORD'),
        'charset' => 'utf8mb4',
      ],
      'storage' => [
        'adapter' => 's3',
        'bucket' => 'tenant-test-tenant-assets',
        'region' => 'eu-central-1',
      ],
      'rabbitmq' => [
        'host' => getenv('RABBITMQ_HOST'),
        'port' => getenv('RABBITMQ_PORT'),
        'user' => 'guest',
        'password' => 'guest',
        'vhost' => '/',
      ],
      'features' => [
        'dms' => false,
        'delphi' => false,
        'externalApi' => false,
        'heapTracking' => false,
      ],
      'tenant' => [
        'id' => 'test-tenant',
        'name' => 'Test Tenant',
        'subdomain' => 'test-tenant',
        'domain' => 'architrave.com',
      ],
    ];
