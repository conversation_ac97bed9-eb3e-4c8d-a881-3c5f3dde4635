apiVersion: v1
kind: ConfigMap
metadata:
  name: php-fpm-config
  namespace: tenant-test-tenant
data:
  www.conf: |
    [www]
    user = www-data
    group = www-data
    listen = 0.0.0.0:9000
    pm = dynamic
    pm.max_children = 5
    pm.start_servers = 2
    pm.min_spare_servers = 1
    pm.max_spare_servers = 3
    chdir = /storage/ArchAssets/public
    php_admin_value[error_log] = /proc/self/fd/2
    php_admin_flag[log_errors] = on
    php_admin_value[memory_limit] = 256M
    php_admin_value[upload_max_filesize] = 50M
    php_admin_value[post_max_size] = 50M
