apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-test-tenant-backend
  namespace: tenant-test-tenant
spec:
  template:
    spec:
      containers:
      - name: backend
        volumeMounts:
        - name: notifications-config-volume
          mountPath: /storage/ArchAssets/config/autoload/notifications.php
          subPath: notifications.php
      volumes:
      - name: notifications-config-volume
        configMap:
          name: notifications-config
