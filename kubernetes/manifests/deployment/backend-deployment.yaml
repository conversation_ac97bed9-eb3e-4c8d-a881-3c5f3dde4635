apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"tenant-test-tenant-backend","component":"backend","tenant":"test-tenant"},"name":"tenant-test-tenant-backend","namespace":"tenant-test-tenant"},"spec":{"replicas":2,"selector":{"matchLabels":{"app":"tenant-test-tenant-backend"}},"template":{"metadata":{"labels":{"app":"tenant-test-tenant-backend","component":"backend","tenant":"test-tenant","tenant.architrave.io/tenant-id":"test-tenant"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"test-tenant"},{"name":"TENANT_NAME","value":"Test Tenant"},{"name":"ENVIRONMENT","value":"production"},{"name":"DB_HOST","valueFrom":{"secretKeyRef":{"key":"host","name":"db-credentials"}}},{"name":"DB_PORT","valueFrom":{"secretKeyRef":{"key":"port","name":"db-credentials"}}},{"name":"DB_NAME","valueFrom":{"secretKeyRef":{"key":"database","name":"db-credentials"}}},{"name":"DB_USER","valueFrom":{"secretKeyRef":{"key":"username","name":"db-credentials"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"password","name":"db-credentials"}}},{"name":"RABBITMQ_HOST","value":"tenant-test-tenant-rabbitmq"},{"name":"RABBITMQ_PORT","value":"5672"}],"image":"545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"backend","ports":[{"containerPort":9000,"name":"http"}],"resources":{"limits":{"cpu":"400m","memory":"512Mi"},"requests":{"cpu":"200m","memory":"256Mi"}},"volumeMounts":[{"mountPath":"/storage","name":"storage-volume"},{"mountPath":"/usr/local/etc/php-fpm.d/www.conf","name":"php-fpm-config","subPath":"www.conf"},{"mountPath":"/storage/ArchAssets/config/autoload/local.php","name":"app-config","subPath":"local.php"}]}],"initContainers":[{"command":["sh","-c","# Create proper directory structure for the application\nmkdir -p /storage/clear \u0026\u0026 chmod 777 /storage/clear \u0026\u0026\nmkdir -p /storage/ArchAssets/config/autoload \u0026\u0026 chmod 777 /storage/ArchAssets/config/autoload \u0026\u0026\nmkdir -p /storage/ArchAssets/public \u0026\u0026 chmod 777 /storage/ArchAssets/public \u0026\u0026\nmkdir -p /storage/ArchAssets/vendor/bin \u0026\u0026 chmod 777 /storage/ArchAssets/vendor/bin \u0026\u0026\n# Create dummy doctrine-module file to prevent errors\necho '#!/bin/sh' \u003e /storage/ArchAssets/vendor/bin/doctrine-module \u0026\u0026\necho 'echo \"Dummy doctrine-module\"' \u003e\u003e /storage/ArchAssets/vendor/bin/doctrine-module \u0026\u0026\nchmod +x /storage/ArchAssets/vendor/bin/doctrine-module\n"],"image":"busybox","name":"init-storage","volumeMounts":[{"mountPath":"/storage","name":"storage-volume"}]}],"serviceAccountName":"tenant-test-tenant-s3-sa","volumes":[{"emptyDir":{},"name":"storage-volume"},{"configMap":{"name":"php-fpm-config"},"name":"php-fpm-config"},{"configMap":{"name":"app-config"},"name":"app-config"}]}}}}
  creationTimestamp: "2025-05-20T07:09:30Z"
  generation: 1
  labels:
    app: tenant-test-tenant-backend
    component: backend
    tenant: test-tenant
  name: tenant-test-tenant-backend
  namespace: tenant-test-tenant
  resourceVersion: "364988"
  uid: 7ccf7446-e8e6-4c54-af44-f16b8b8c6c8d
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: tenant-test-tenant-backend
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: tenant-test-tenant-backend
        component: backend
        tenant: test-tenant
        tenant.architrave.io/tenant-id: test-tenant
    spec:
      containers:
      - env:
        - name: TENANT_ID
          value: test-tenant
        - name: TENANT_NAME
          value: Test Tenant
        - name: ENVIRONMENT
          value: production
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              key: host
              name: db-credentials
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              key: port
              name: db-credentials
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              key: database
              name: db-credentials
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              key: username
              name: db-credentials
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: db-credentials
        - name: RABBITMQ_HOST
          value: tenant-test-tenant-rabbitmq
        - name: RABBITMQ_PORT
          value: "5672"
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
        imagePullPolicy: IfNotPresent
        name: backend
        ports:
        - containerPort: 9000
          name: http
          protocol: TCP
        resources:
          limits:
            cpu: 200m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 256Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /storage
          name: storage-volume
        - mountPath: /usr/local/etc/php-fpm.d/www.conf
          name: php-fpm-config
          subPath: www.conf
        - mountPath: /storage/ArchAssets/config/autoload/local.php
          name: app-config
          subPath: local.php
      dnsPolicy: ClusterFirst
      initContainers:
      - command:
        - sh
        - -c
        - |
          # Create proper directory structure for the application
          mkdir -p /storage/clear && chmod 777 /storage/clear &&
          mkdir -p /storage/ArchAssets/config/autoload && chmod 777 /storage/ArchAssets/config/autoload &&
          mkdir -p /storage/ArchAssets/public && chmod 777 /storage/ArchAssets/public &&
          mkdir -p /storage/ArchAssets/vendor/bin && chmod 777 /storage/ArchAssets/vendor/bin &&
          # Create dummy doctrine-module file to prevent errors
          echo '#!/bin/sh' > /storage/ArchAssets/vendor/bin/doctrine-module &&
          echo 'echo "Dummy doctrine-module"' >> /storage/ArchAssets/vendor/bin/doctrine-module &&
          chmod +x /storage/ArchAssets/vendor/bin/doctrine-module
        image: busybox
        imagePullPolicy: Always
        name: init-storage
        resources:
          limits:
            cpu: 100m
            memory: 256Mi
          requests:
            cpu: 50m
            memory: 128Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /storage
          name: storage-volume
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: tenant-test-tenant-s3-sa
      serviceAccountName: tenant-test-tenant-s3-sa
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: storage-volume
      - configMap:
          defaultMode: 420
          name: php-fpm-config
        name: php-fpm-config
      - configMap:
          defaultMode: 420
          name: app-config
        name: app-config
status:
  conditions:
  - lastTransitionTime: "2025-05-20T07:09:30Z"
    lastUpdateTime: "2025-05-20T07:09:30Z"
    message: Deployment does not have minimum availability.
    reason: MinimumReplicasUnavailable
    status: "False"
    type: Available
  - lastTransitionTime: "2025-05-20T07:09:30Z"
    lastUpdateTime: "2025-05-20T07:09:30Z"
    message: 'pods "tenant-test-tenant-backend-9c747cfb8-nq4h7" is forbidden: exceeded
      quota: test-tenant-quota, requested: limits.cpu=2400m, used: limits.cpu=9800m,
      limited: limits.cpu=10'
    reason: FailedCreate
    status: "True"
    type: ReplicaFailure
  - lastTransitionTime: "2025-05-20T07:09:30Z"
    lastUpdateTime: "2025-05-20T07:09:30Z"
    message: ReplicaSet "tenant-test-tenant-backend-9c747cfb8" is progressing.
    reason: ReplicaSetUpdated
    status: "True"
    type: Progressing
  observedGeneration: 1
  replicas: 1
  unavailableReplicas: 2
  updatedReplicas: 1
