apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-test-tenant-frontend
  namespace: tenant-test-tenant
spec:
  template:
    spec:
      containers:
      - name: frontend
        volumeMounts:
        - name: nginx-config-volume
          mountPath: /etc/nginx/conf.d/default.conf
          subPath: default.conf
      volumes:
      - name: nginx-config-volume
        configMap:
          name: nginx-config
