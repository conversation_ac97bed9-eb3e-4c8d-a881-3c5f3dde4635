apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "4"
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{"deployment.kubernetes.io/revision":"3"},"creationTimestamp":"2025-05-16T14:32:50Z","generation":3,"labels":{"app":"tenant-check-testnew-backend","component":"backend","tenant":"check-testnew"},"name":"tenant-check-testnew-backend","namespace":"tenant-check-testnew","resourceVersion":"830057","uid":"52c81cdc-df9b-4fd4-9c8a-74fa0bff2a3c"},"spec":{"progressDeadlineSeconds":600,"replicas":2,"revisionHistoryLimit":10,"selector":{"matchLabels":{"app":"tenant-check-testnew-backend"}},"strategy":{"rollingUpdate":{"maxSurge":"25%","maxUnavailable":"25%"},"type":"RollingUpdate"},"template":{"metadata":{"annotations":{"kubectl.kubernetes.io/restartedAt":"2025-05-16T17:28:21+02:00"},"creationTimestamp":null,"labels":{"app":"tenant-check-testnew-backend","component":"backend","tenant":"check-testnew","tenant.architrave.io/tenant-id":"check-testnew"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"check-testnew"},{"name":"TENANT_NAME","value":"Check Test New"},{"name":"ENVIRONMENT","value":"production"},{"name":"DB_HOST","valueFrom":{"secretKeyRef":{"key":"host","name":"db-credentials"}}},{"name":"DB_PORT","valueFrom":{"secretKeyRef":{"key":"port","name":"db-credentials"}}},{"name":"DB_NAME","valueFrom":{"secretKeyRef":{"key":"database","name":"db-credentials"}}},{"name":"DB_USER","valueFrom":{"secretKeyRef":{"key":"username","name":"db-credentials"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"password","name":"db-credentials"}}},{"name":"RABBITMQ_HOST","value":"tenant-check-testnew-rabbitmq"},{"name":"RABBITMQ_PORT","value":"5672"}],"image":"545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","imagePullPolicy":"IfNotPresent","name":"backend","ports":[{"containerPort":9000,"name":"http","protocol":"TCP"}],"resources":{"limits":{"cpu":"400m","memory":"512Mi"},"requests":{"cpu":"200m","memory":"256Mi"}},"securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"readOnlyRootFilesystem":true},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","volumeMounts":[{"mountPath":"/storage","name":"storage-volume"},{"mountPath":"/usr/local/etc/php-fpm.d/www.conf","name":"php-fpm-config","subPath":"www.conf"},{"mountPath":"/storage/ArchAssets/config/autoload/local.php","name":"app-config","subPath":"local.php"}]}],"dnsPolicy":"ClusterFirst","initContainers":[{"command":["sh","-c","# Create proper directory structure for the application\nmkdir -p /storage/clear \u0026\u0026 chmod 777 /storage/clear \u0026\u0026\nmkdir -p /storage/ArchAssets/config/autoload \u0026\u0026 chmod 777 /storage/ArchAssets/config/autoload \u0026\u0026\nmkdir -p /storage/ArchAssets/public \u0026\u0026 chmod 777 /storage/ArchAssets/public \u0026\u0026\nmkdir -p /storage/ArchAssets/vendor/bin \u0026\u0026 chmod 777 /storage/ArchAssets/vendor/bin \u0026\u0026\n# Create dummy doctrine-module file to prevent errors\necho '#!/bin/sh' \u003e /storage/ArchAssets/vendor/bin/doctrine-module \u0026\u0026\necho 'echo \"Dummy doctrine-module\"' \u003e\u003e /storage/ArchAssets/vendor/bin/doctrine-module \u0026\u0026\nchmod +x /storage/ArchAssets/vendor/bin/doctrine-module\n"],"image":"busybox","imagePullPolicy":"Always","name":"init-storage","resources":{},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","volumeMounts":[{"mountPath":"/storage","name":"storage-volume"}]}],"restartPolicy":"Always","schedulerName":"default-scheduler","securityContext":{"fsGroup":1000,"runAsGroup":1000,"runAsNonRoot":true,"runAsUser":1000,"seccompProfile":{"type":"RuntimeDefault"}},"serviceAccount":"tenant-check-testnew-s3-sa","serviceAccountName":"tenant-check-testnew-s3-sa","terminationGracePeriodSeconds":30,"volumes":[{"emptyDir":{},"name":"storage-volume"},{"configMap":{"defaultMode":420,"name":"php-fpm-config"},"name":"php-fpm-config"},{"configMap":{"defaultMode":420,"name":"app-config"},"name":"app-config"}]}}},"status":{"availableReplicas":2,"conditions":[{"lastTransitionTime":"2025-05-16T14:32:56Z","lastUpdateTime":"2025-05-16T14:32:56Z","message":"Deployment has minimum availability.","reason":"MinimumReplicasAvailable","status":"True","type":"Available"},{"lastTransitionTime":"2025-05-16T14:32:50Z","lastUpdateTime":"2025-05-16T15:28:32Z","message":"ReplicaSet \"tenant-check-testnew-backend-576b7c6fd8\" has successfully progressed.","reason":"NewReplicaSetAvailable","status":"True","type":"Progressing"}],"observedGeneration":3,"readyReplicas":2,"replicas":2,"updatedReplicas":2}}
  creationTimestamp: "2025-05-16T14:32:50Z"
  generation: 4
  labels:
    app: tenant-check-testnew-backend
    component: backend
    tenant: check-testnew
  name: tenant-check-testnew-backend
  namespace: tenant-check-testnew
  resourceVersion: "830930"
  uid: 52c81cdc-df9b-4fd4-9c8a-74fa0bff2a3c
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: tenant-check-testnew-backend
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: "2025-05-16T17:28:21+02:00"
      creationTimestamp: null
      labels:
        app: tenant-check-testnew-backend
        component: backend
        tenant: check-testnew
        tenant.architrave.io/tenant-id: check-testnew
    spec:
      containers:
      - env:
        - name: TENANT_ID
          value: check-testnew
        - name: TENANT_NAME
          value: Check Test New
        - name: ENVIRONMENT
          value: production
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              key: host
              name: db-credentials
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              key: port
              name: db-credentials
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              key: database
              name: db-credentials
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              key: username
              name: db-credentials
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: db-credentials
        - name: RABBITMQ_HOST
          value: tenant-check-testnew-rabbitmq
        - name: RABBITMQ_PORT
          value: "5672"
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
        imagePullPolicy: IfNotPresent
        name: backend
        ports:
        - containerPort: 9000
          name: http
          protocol: TCP
        resources:
          limits:
            cpu: 400m
            memory: 512Mi
          requests:
            cpu: 200m
            memory: 256Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /storage
          name: storage-volume
        - mountPath: /usr/local/etc/php-fpm.d/www.conf
          name: php-fpm-config
          subPath: www.conf
        - mountPath: /storage/ArchAssets/config/autoload/local.php
          name: app-config
          subPath: local.php
      dnsPolicy: ClusterFirst
      initContainers:
      - command:
        - sh
        - -c
        - |
          # Create proper directory structure for the application
          mkdir -p /storage/clear && chmod 777 /storage/clear &&
          mkdir -p /storage/ArchAssets/config/autoload && chmod 777 /storage/ArchAssets/config/autoload &&
          mkdir -p /storage/ArchAssets/public && chmod 777 /storage/ArchAssets/public &&
          mkdir -p /storage/ArchAssets/vendor/bin && chmod 777 /storage/ArchAssets/vendor/bin &&
          # Create dummy doctrine-module file to prevent errors
          echo '#!/bin/sh' > /storage/ArchAssets/vendor/bin/doctrine-module &&
          echo 'echo "Dummy doctrine-module"' >> /storage/ArchAssets/vendor/bin/doctrine-module &&
          chmod +x /storage/ArchAssets/vendor/bin/doctrine-module
        image: busybox
        imagePullPolicy: Always
        name: init-storage
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /storage
          name: storage-volume
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000
        seccompProfile:
          type: RuntimeDefault
      serviceAccount: tenant-check-testnew-s3-sa
      serviceAccountName: tenant-check-testnew-s3-sa
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: storage-volume
      - configMap:
          defaultMode: 420
          name: php-fpm-config
        name: php-fpm-config
      - configMap:
          defaultMode: 420
          name: app-config
        name: app-config
status:
  availableReplicas: 2
  conditions:
  - lastTransitionTime: "2025-05-16T14:32:50Z"
    lastUpdateTime: "2025-05-16T15:32:00Z"
    message: ReplicaSet "tenant-check-testnew-backend-749945d64" has successfully
      progressed.
    reason: NewReplicaSetAvailable
    status: "True"
    type: Progressing
  - lastTransitionTime: "2025-05-16T15:32:13Z"
    lastUpdateTime: "2025-05-16T15:32:13Z"
    message: Deployment has minimum availability.
    reason: MinimumReplicasAvailable
    status: "True"
    type: Available
  observedGeneration: 4
  readyReplicas: 2
  replicas: 2
  updatedReplicas: 2
