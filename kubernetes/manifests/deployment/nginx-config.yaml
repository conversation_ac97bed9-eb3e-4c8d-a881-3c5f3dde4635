apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: tenant-test-tenant
data:
  default.conf: |
    proxy_cache_path /tmp/nginx/ keys_zone=newsfeed_cache:1m max_size=50m use_temp_path=off inactive=1h;

    map $http_origin $allow_origin {
          default "";
          "~^https?://(?:[^/]*\.)?(sb\.architrave\.(?:cloud))|(s3\.eu-central-1\.amazonaws\.com)(?::[0-9]+)?$" "$http_origin";
    }

    server {
        listen                443 ssl;
        ssl_certificate       /etc/nginx/cert/architrave.crt;
        ssl_certificate_key   /etc/nginx/cert/architrave.key;
        ssl_session_cache shared:SSL:20m;
        ssl_session_timeout 180m;

        add_header Cache-Control "private, no-cache, no-store, must-revalidate, max-age=0";

        add_header Strict-Transport-Security "max-age=63072000; includeSubdomains; preload";
        add_header X-Content-Type-Options nosniff;
        add_header Content-Security-Policy "default-src 'self'; style-src 'unsafe-inline' 'self'";
        add_header Permissions-Policy "accelerometer=(), ambient-light-sensor=(), autoplay=(), battery=(), camera=(), cross-origin-isolated=(), display-capture=(), document-domain=(), encrypted-media=(), execution-while-not-rendered=(), execution-while-out-of-viewport=(), fullscreen=(), geolocation=(), gyroscope=(), keyboard-map=(), magnetometer=(), microphone=(), midi=(), navigation-override=(), payment=(), picture-in-picture=(), publickey-credentials-get=(), screen-wake-lock=(), sync-xhr=(), usb=(), web-share=(), xr-spatial-tracking=(), clipboard-read=(self), clipboard-write=(self), gamepad=(), speaker-selection=(), conversion-measurement=(), focus-without-user-activation=(), hid=(), idle-detection=(), interest-cohort=(), serial=(), sync-script=(), trust-token-redemption=(), window-placement=(), vertical-scroll=()";
        add_header X-Frame-Options "SAMEORIGIN";
        add_header Referrer-Policy same-origin;
        add_header X-XSS-Protection 1 always;
        proxy_cookie_path / "/; HTTPOnly; Secure";

        proxy_headers_hash_bucket_size 512;

        server_name           test-tenant.architrave.com;
        access_log            /var/log/nginx/architrave-ssl.access.log;
        access_log            /var/log/nginx/architrave-ssl.access.log main;

        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location /api {
            proxy_pass http://webapp:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    server {
        listen 80;
        server_name test-tenant.architrave.com;
        return 301 https://$host$request_uri;
    }
