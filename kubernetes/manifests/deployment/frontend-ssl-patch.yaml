apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-test-tenant-frontend
  namespace: tenant-test-tenant
spec:
  template:
    spec:
      containers:
      - name: frontend
        volumeMounts:
        - name: ssl-cert-volume
          mountPath: /etc/nginx/cert
      volumes:
      - name: ssl-cert-volume
        secret:
          secretName: ssl-cert
          items:
          - key: tls.crt
            path: architrave.crt
          - key: tls.key
            path: architrave.key
