apiVersion: v1
kind: Pod
metadata:
  name: db-bastion
  namespace: tenant-check-test
spec:
  containers:
  - name: mysql-client
    image: mysql:8.0
    command:
    - /bin/bash
    - -c
    - |
      echo "Installing required packages..."
      apt-get update && apt-get install -y curl netcat-openbsd dnsutils

      echo "Testing DNS resolution..."
      nslookup production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com

      echo "Testing connection to RDS..."
      nc -zv production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com 3306

      echo "Sleeping to keep the pod running..."
      sleep 3600
    resources:
      limits:
        memory: "128Mi"
        cpu: "100m"
  restartPolicy: Never
