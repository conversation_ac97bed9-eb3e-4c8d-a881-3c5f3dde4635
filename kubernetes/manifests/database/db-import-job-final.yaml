apiVersion: batch/v1
kind: Job
metadata:
  name: tenant-check-test-db-import-final
  namespace: tenant-check-test
spec:
  ttlSecondsAfterFinished: 3600
  template:
    spec:
      containers:
      - name: db-import
        image: mysql:8.0
        command:
        - /bin/bash
        - -c
        - |
          # Install AWS CLI
          apt-get update && apt-get install -y curl unzip
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          ./aws/install
          
          # Set variables
          TENANT_ID="check-test"
          DB_NAME="tenant_${TENANT_ID}"
          DB_USER="tenant_${TENANT_ID}"
          DB_PASSWORD="Secure123Password!"
          
          # Use the correct RDS endpoint
          RDS_HOST="production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
          RDS_PORT="3306"
          RDS_ADMIN_USER="admin"
          RDS_ADMIN_PASSWORD="1O\$JbjX%\$Gnh9LNg"
          
          S3_BUCKET="architravetestdb"
          S3_KEY="architrave_1.45.2.sql"
          
          echo "Testing connection to RDS..."
          if mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SELECT 1" &>/dev/null; then
            echo "Connection to RDS successful"
          else
            echo "Failed to connect to RDS"
            exit 1
          fi
          
          echo "Creating database schema and user for tenant $TENANT_ID"
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" <<EOF
          CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
          CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASSWORD';
          GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'%';
          FLUSH PRIVILEGES;
          EOF
          
          if [ $? -ne 0 ]; then
            echo "Failed to create database schema and user"
            exit 1
          fi
          
          echo "Database schema and user created successfully"
          
          # Check if the S3 object exists
          echo "Checking if SQL file exists in S3..."
          if aws s3 ls "s3://$S3_BUCKET/$S3_KEY"; then
            echo "SQL file found in S3"
            
            # Import SQL file directly from S3 to RDS
            echo "Importing SQL file from S3 to RDS..."
            aws s3 cp "s3://$S3_BUCKET/$S3_KEY" - | mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" "$DB_NAME"
            
            if [ $? -ne 0 ]; then
              echo "Failed to import SQL file from S3 to RDS"
              exit 1
            fi
            
            echo "SQL file imported successfully"
          else
            echo "SQL file not found in S3, creating sample tables instead"
            
            # Create sample tables
            mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" "$DB_NAME" <<EOF
            CREATE TABLE IF NOT EXISTS users (
              id INT AUTO_INCREMENT PRIMARY KEY,
              username VARCHAR(255) NOT NULL,
              email VARCHAR(255) NOT NULL,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE TABLE IF NOT EXISTS documents (
              id INT AUTO_INCREMENT PRIMARY KEY,
              title VARCHAR(255) NOT NULL,
              content TEXT,
              user_id INT,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (user_id) REFERENCES users(id)
            );
            
            -- Insert sample data
            INSERT INTO users (username, email) VALUES
              ('user1', '<EMAIL>'),
              ('user2', '<EMAIL>');
            
            INSERT INTO documents (title, content, user_id) VALUES
              ('Document 1', 'This is the content of document 1', 1),
              ('Document 2', 'This is the content of document 2', 1),
              ('Document 3', 'This is the content of document 3', 2);
            EOF
            
            if [ $? -ne 0 ]; then
              echo "Failed to create sample tables"
              exit 1
            fi
            
            echo "Sample tables created successfully"
          fi
          
          # Update the Kubernetes secret with the database credentials
          cat <<EOF > /tmp/db-credentials.yaml
          apiVersion: v1
          kind: Secret
          metadata:
            name: db-credentials
            namespace: tenant-$TENANT_ID
          type: Opaque
          stringData:
            host: "$RDS_HOST"
            port: "$RDS_PORT"
            database: "$DB_NAME"
            username: "$DB_USER"
            password: "$DB_PASSWORD"
          EOF
          
          kubectl apply -f /tmp/db-credentials.yaml
          
          echo "Database credentials secret updated successfully"
          echo "Database setup completed successfully for tenant $TENANT_ID"
      restartPolicy: Never
  backoffLimit: 3
