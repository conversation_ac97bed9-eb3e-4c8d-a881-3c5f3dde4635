apiVersion: v1
kind: Pod
metadata:
  name: create-db
  namespace: tenant-check-testnew
spec:
  containers:
  - name: mysql-client
    image: mysql:8.0
    command:
    - /bin/bash
    - -c
    - |
      # Set variables
      TENANT_ID="check-testnew"
      DB_NAME="tenant_${TENANT_ID}"
      DB_USER="tenant_${TENANT_ID}"
      DB_PASSWORD=$(cat /db-creds/password)
      
      # Get admin credentials
      RDS_ADMIN_USER=$(cat /admin-creds/username)
      RDS_ADMIN_PASSWORD=$(cat /admin-creds/password)
      
      # Set RDS host
      RDS_HOST="production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
      RDS_PORT="3306"
      
      echo "Creating database and user..."
      mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "
      CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
      CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASSWORD';
      GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'%';
      FLUSH PRIVILEGES;
      "
      
      echo "Creating test table..."
      mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" "$DB_NAME" -e "
      CREATE TABLE IF NOT EXISTS test_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      INSERT INTO test_table (name) VALUES ('Test Record');
      "
      
      echo "Database setup completed successfully!"
      
      # Keep the pod running for debugging
      sleep 3600
    volumeMounts:
    - name: db-creds
      mountPath: /db-creds
    - name: admin-creds
      mountPath: /admin-creds
  volumes:
  - name: db-creds
    secret:
      secretName: db-credentials
  - name: admin-creds
    secret:
      secretName: rds-admin-credentials
