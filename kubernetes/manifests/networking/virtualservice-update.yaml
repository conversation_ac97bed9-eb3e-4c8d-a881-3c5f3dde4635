apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-test-tenant-vs
  namespace: tenant-test-tenant
  labels:
    tenant: test-tenant
spec:
  hosts:
  - "test-tenant.architrave-assets.com"
  gateways:
  - "istio-system/tenant-gateway"
  http:
  - match:
    - uri:
        prefix: "/api"
    retries:
      attempts: 3
      perTryTimeout: 2s
      retryOn: gateway-error,connect-failure,refused-stream
    route:
    - destination:
        host: webapp
        port:
          number: 8080
    timeout: 5s
  - route:
    - destination:
        host: tenant-test-tenant-frontend
        port:
          number: 80
