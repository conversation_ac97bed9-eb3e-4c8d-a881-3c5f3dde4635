apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-check-testnew-rabbitmq-policy
  namespace: tenant-check-testnew
spec:
  podSelector:
    matchLabels:
      app: tenant-check-testnew-rabbitmq
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: tenant-check-testnew-backend
    ports:
    - protocol: TCP
      port: 5672
    - protocol: TCP
      port: 15672
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: kube-system
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-system
