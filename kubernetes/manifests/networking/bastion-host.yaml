apiVersion: apps/v1
kind: Deployment
metadata:
  name: db-bastion
  namespace: tenant-test-tenant
spec:
  replicas: 1
  selector:
    matchLabels:
      app: db-bastion
  template:
    metadata:
      labels:
        app: db-bastion
      annotations:
        sidecar.istio.io/inject: "false"
    spec:
      containers:
      - name: mysql-client
        image: mysql:8.0
        command: ["sleep", "infinity"]
        resources:
          limits:
            cpu: 50m
            memory: 128Mi
          requests:
            cpu: 25m
            memory: 64Mi
        env:
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: host
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: port
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: database
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: password
