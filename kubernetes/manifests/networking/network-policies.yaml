apiVersion: v1
items:
- apiVersion: networking.k8s.io/v1
  kind: NetworkPolicy
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"networking.k8s.io/v1","kind":"NetworkPolicy","metadata":{"annotations":{},"name":"tenant-check-testnew-backend-policy","namespace":"tenant-check-testnew"},"spec":{"egress":[{"to":[{"podSelector":{"matchLabels":{"app":"tenant-check-testnew-rabbitmq"}}},{"namespaceSelector":{"matchLabels":{"kubernetes.io/metadata.name":"kube-system"}}},{"namespaceSelector":{"matchLabels":{"kubernetes.io/metadata.name":"istio-system"}}}]}],"ingress":[{"from":[{"podSelector":{"matchLabels":{"app":"tenant-check-testnew-frontend"}}}]}],"podSelector":{"matchLabels":{"app":"tenant-check-testnew-backend"}},"policyTypes":["Ingress","Egress"]}}
    creationTimestamp: "2025-05-16T14:32:59Z"
    generation: 1
    name: tenant-check-testnew-backend-policy
    namespace: tenant-check-testnew
    resourceVersion: "818430"
    uid: bb7770fd-3db7-4e8d-a507-be9dcbf29c95
  spec:
    egress:
    - to:
      - podSelector:
          matchLabels:
            app: tenant-check-testnew-rabbitmq
      - namespaceSelector:
          matchLabels:
            kubernetes.io/metadata.name: kube-system
      - namespaceSelector:
          matchLabels:
            kubernetes.io/metadata.name: istio-system
    ingress:
    - from:
      - podSelector:
          matchLabels:
            app: tenant-check-testnew-frontend
    podSelector:
      matchLabels:
        app: tenant-check-testnew-backend
    policyTypes:
    - Ingress
    - Egress
- apiVersion: networking.k8s.io/v1
  kind: NetworkPolicy
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"networking.k8s.io/v1","kind":"NetworkPolicy","metadata":{"annotations":{},"name":"tenant-check-testnew-frontend-policy","namespace":"tenant-check-testnew"},"spec":{"egress":[{"to":[{"podSelector":{"matchLabels":{"app":"tenant-check-testnew-backend"}}},{"namespaceSelector":{"matchLabels":{"kubernetes.io/metadata.name":"kube-system"}}},{"namespaceSelector":{"matchLabels":{"kubernetes.io/metadata.name":"istio-system"}}}]}],"ingress":[{"from":[{"namespaceSelector":{"matchLabels":{"istio-injection":"enabled"}}}]}],"podSelector":{"matchLabels":{"app":"tenant-check-testnew-frontend"}},"policyTypes":["Ingress","Egress"]}}
    creationTimestamp: "2025-05-16T14:32:59Z"
    generation: 1
    name: tenant-check-testnew-frontend-policy
    namespace: tenant-check-testnew
    resourceVersion: "818427"
    uid: 66fe19c5-1965-4cda-bddb-57258aeb9f3d
  spec:
    egress:
    - to:
      - podSelector:
          matchLabels:
            app: tenant-check-testnew-backend
      - namespaceSelector:
          matchLabels:
            kubernetes.io/metadata.name: kube-system
      - namespaceSelector:
          matchLabels:
            kubernetes.io/metadata.name: istio-system
    ingress:
    - from:
      - namespaceSelector:
          matchLabels:
            istio-injection: enabled
    podSelector:
      matchLabels:
        app: tenant-check-testnew-frontend
    policyTypes:
    - Ingress
    - Egress
kind: List
metadata:
  resourceVersion: ""
