apiVersion: v1
kind: ServiceAccount
metadata:
  name: ebs-csi-controller-sa
  namespace: kube-system
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/AmazonEKS_EBS_CSI_DriverRole
---
apiVersion: helm.cattle.io/v1
kind: Helm<PERSON>hart
metadata:
  name: aws-ebs-csi-driver
  namespace: kube-system
spec:
  chart: aws-ebs-csi-driver
  repo: https://kubernetes-sigs.github.io/aws-ebs-csi-driver
  targetNamespace: kube-system
  set:
    controller.serviceAccount.create: "false"
    controller.serviceAccount.name: "ebs-csi-controller-sa"
