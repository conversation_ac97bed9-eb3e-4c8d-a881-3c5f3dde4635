apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tenant-gateway
  namespace: istio-system
spec:
  selector:
    istio: ingressgateway
  servers:
  - hosts:
    - '*.architrave-assets.de'
    port:
      name: http
      number: 80
      protocol: HTTP
    tls:
      httpsRedirect: true
  - hosts:
    - '*.architrave-assets.de'
    port:
      name: https
      number: 443
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: architrave-assets-wildcard-cert
