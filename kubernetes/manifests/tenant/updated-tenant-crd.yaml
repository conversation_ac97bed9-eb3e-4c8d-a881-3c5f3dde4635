apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: tenants.tenant.architrave.io
spec:
  group: tenant.architrave.io
  names:
    kind: Tenant
    listKind: TenantList
    plural: tenants
    singular: tenant
    shortNames:
      - tn
  scope: Cluster
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                displayName:
                  description: "Display name of the tenant"
                  type: string
                subdomain:
                  description: "Subdomain for the tenant"
                  type: string
                environment:
                  description: "Environment for the tenant"
                  type: string
                tier:
                  description: "Tier of the tenant (e.g., standard, premium)"
                  type: string
                status:
                  description: "Status of the tenant"
                  type: string
                creationDate:
                  description: "Creation date of the tenant"
                  type: string
                features:
                  type: object
                  properties:
                    dms:
                      type: boolean
                    delphi:
                      type: boolean
                    externalApi:
                      type: boolean
                    heapTracking:
                      type: boolean
                resourceQuota:
                  type: object
                  properties:
                    cpu:
                      description: "CPU quota for the tenant"
                      type: string
                    memory:
                      description: "Memory quota for the tenant"
                      type: string
                    storage:
                      description: "Storage quota for the tenant"
                      type: string
                    pods:
                      description: "Pod quota for the tenant"
                      type: integer
                configuration:
                  type: object
                  properties:
                    language:
                      type: string
                    documentClassSet:
                      type: string
                    referenceData:
                      type: string
                    logo:
                      type: string
              required:
                - displayName
                - subdomain
            status:
              type: object
              properties:
                phase:
                  description: "Current phase of the tenant (e.g., Pending, Active, Terminating)"
                  type: string
                conditions:
                  type: array
                  items:
                    type: object
                    properties:
                      type:
                        type: string
                      status:
                        type: string
                      reason:
                        type: string
                      message:
                        type: string
                      lastTransitionTime:
                        type: string
                        format: date-time
                    required:
                      - type
                      - status
                resources:
                  type: object
                  properties:
                    namespace:
                      type: string
                    database:
                      type: string
                    storage:
                      type: string
      additionalPrinterColumns:
        - name: Display Name
          type: string
          jsonPath: .spec.displayName
        - name: Subdomain
          type: string
          jsonPath: .spec.subdomain
        - name: Environment
          type: string
          jsonPath: .spec.environment
        - name: Tier
          type: string
          jsonPath: .spec.tier
        - name: Status
          type: string
          jsonPath: .spec.status
        - name: Age
          type: date
          jsonPath: .metadata.creationTimestamp
      subresources:
        status: {}
