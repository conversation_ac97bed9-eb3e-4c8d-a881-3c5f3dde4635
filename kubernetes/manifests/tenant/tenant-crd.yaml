apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: tenants.tenant.architrave.io
spec:
  group: tenant.architrave.io
  names:
    kind: Tenant
    listKind: TenantList
    plural: tenants
    singular: tenant
  scope: Cluster
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              displayName:
                type: string
              subdomain:
                type: string
              environment:
                type: string
              tier:
                type: string
              status:
                type: string
              creationDate:
                type: string
              features:
                type: object
                properties:
                  dms:
                    type: boolean
                  delphi:
                    type: boolean
                  externalApi:
                    type: boolean
                  heapTracking:
                    type: boolean
              resourceQuota:
                type: object
                properties:
                  cpu:
                    type: string
                  memory:
                    type: string
                  storage:
                    type: string
                  pods:
                    type: integer
              configuration:
                type: object
                properties:
                  language:
                    type: string
                  documentClassSet:
                    type: string
                  referenceData:
                    type: string
                  logo:
                    type: string
          status:
            type: object
            properties:
              phase:
                type: string
              message:
                type: string
              lastUpdateTime:
                type: string
    subresources:
      status: {}
