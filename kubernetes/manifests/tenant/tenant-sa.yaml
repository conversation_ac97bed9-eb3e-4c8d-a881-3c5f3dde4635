apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-check-test-sa
  namespace: tenant-check-test
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: tenant-check-test-role
  namespace: tenant-check-test
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "create", "update", "patch", "delete", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tenant-check-test-rolebinding
  namespace: tenant-check-test
subjects:
- kind: ServiceAccount
  name: tenant-check-test-sa
  namespace: tenant-check-test
roleRef:
  kind: Role
  name: tenant-check-test-role
  apiGroup: rbac.authorization.k8s.io
