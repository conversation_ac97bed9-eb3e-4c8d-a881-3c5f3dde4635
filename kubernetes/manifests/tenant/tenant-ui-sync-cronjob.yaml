apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-management-config
  namespace: default
data:
  api_url: "http://tenant-management-api.default.svc.cluster.local/api"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-ui-sync
  namespace: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tenant-reader
rules:
- apiGroups: ["tenant.architrave.io"]
  resources: ["tenants"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tenant-ui-sync-tenant-reader
subjects:
- kind: ServiceAccount
  name: tenant-ui-sync
  namespace: default
roleRef:
  kind: ClusterRole
  name: tenant-reader
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: tenant-ui-sync
  namespace: default
spec:
  schedule: "*/15 * * * *"  # Run every 15 minutes
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: tenant-ui-sync
          containers:
          - name: tenant-ui-sync
            image: mysql:8.0
            imagePullPolicy: IfNotPresent
            command:
            - /bin/bash
            - -c
            - |
              #!/bin/bash

              # Text colors
              RED='\033[0;31m'
              GREEN='\033[0;32m'
              YELLOW='\033[0;33m'
              BLUE='\033[0;34m'
              NC='\033[0m' # No Color

              # Function to log messages
              log() {
                local level=$1
                local message=$2
                local timestamp=$(date +"%Y-%m-%d %H:%M:%S")

                case $level in
                  INFO)
                    echo -e "${GREEN}[INFO]${NC} ${timestamp} - ${message}"
                    ;;
                  WARN)
                    echo -e "${YELLOW}[WARN]${NC} ${timestamp} - ${message}"
                    ;;
                  ERROR)
                    echo -e "${RED}[ERROR]${NC} ${timestamp} - ${message}"
                    ;;
                  DEBUG)
                    echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - ${message}"
                    ;;
                  *)
                    echo -e "${timestamp} - ${message}"
                    ;;
                esac
              }

              log INFO "Starting tenant management UI synchronization..."

              # Get the list of tenants from Kubernetes
              log INFO "Getting list of tenants from Kubernetes..."
              TENANT_LIST=$(kubectl get tenants.tenant.architrave.io -o json 2>/dev/null || echo '{"items":[]}')
              TENANT_COUNT=$(echo "$TENANT_LIST" | jq '.items | length')

              log INFO "Found $TENANT_COUNT tenants in Kubernetes"

              # Try to get the API URL from ConfigMap
              API_URL=$(kubectl get configmap tenant-management-config -o jsonpath='{.data.api_url}' 2>/dev/null || echo "")

              if [ -n "$API_URL" ]; then
                log INFO "Found tenant management API URL: $API_URL"

                # Send the tenant list to the API
                if curl -s -X POST "$API_URL/sync" \
                     -H "Content-Type: application/json" \
                     -d "$TENANT_LIST" | grep -q "success"; then
                  log INFO "Successfully updated tenant management system via API"
                  exit 0
                else
                  log WARN "Failed to update tenant management system via API, trying database connection..."
                fi
              else
                log INFO "No API URL found in ConfigMap, trying database connection..."
              fi

              # Get database credentials from Kubernetes Secret
              log INFO "Getting database credentials from Kubernetes Secret..."

              # Get credentials from mounted secret
              if [ -f "/var/run/secrets/tenant-management/host" ]; then
                DB_HOST=$(cat /var/run/secrets/tenant-management/host)
                DB_PORT=$(cat /var/run/secrets/tenant-management/port)
                DB_NAME=$(cat /var/run/secrets/tenant-management/database)
                DB_USER=$(cat /var/run/secrets/tenant-management/username)
                DB_PASSWORD=$(cat /var/run/secrets/tenant-management/password)

                log INFO "Successfully retrieved database credentials from Kubernetes Secret"

                # Create a temporary SQL file
                SQL_FILE=$(mktemp)

                # Start transaction
                echo "START TRANSACTION;" >> "$SQL_FILE"

                # Mark all tenants as deleted first
                echo "UPDATE tenants SET status = 'Deleted', updated_at = NOW() WHERE status != 'Deleted';" >> "$SQL_FILE"

                # Update tenants that still exist in Kubernetes
                if [ "$TENANT_COUNT" -gt 0 ]; then
                  echo "$TENANT_LIST" | jq -r '.items[] | "UPDATE tenants SET status = '\''Active'\'', updated_at = NOW() WHERE tenant_id = '\''" + .metadata.name + "'\'';"' >> "$SQL_FILE"
                fi

                # Commit transaction
                echo "COMMIT;" >> "$SQL_FILE"

                # Execute the SQL file
                if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$SQL_FILE"; then
                  log INFO "Successfully updated tenant management database"
                  rm -f "$SQL_FILE"
                  exit 0
                else
                  log ERROR "Failed to update tenant management database"
                  rm -f "$SQL_FILE"
                  exit 1
                fi
              else
                log ERROR "Could not find database credentials in Kubernetes Secret"
                exit 1
              fi
            env:
            - name: AWS_REGION
              value: "eu-central-1"
            volumeMounts:
            - name: tenant-management-secret
              mountPath: "/var/run/secrets/tenant-management"
              readOnly: true
          volumes:
          - name: tenant-management-secret
            secret:
              secretName: tenant-management-db-credentials
              optional: true
          restartPolicy: OnFailure
---
apiVersion: v1
kind: Secret
metadata:
  name: tenant-management-db-credentials
  namespace: default
type: Opaque
stringData:
  # Database credentials for tenant management system
  username: "root"
  password: "password123"
  host: "production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
  port: "3306"
  database: "tenant_management"
