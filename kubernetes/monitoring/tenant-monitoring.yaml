apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: tenant-${TENANT_ID}-monitor
  namespace: monitoring
  labels:
    app: tenant-${TENANT_ID}
    release: prometheus
spec:
  selector:
    matchLabels:
      app: tenant-${TENANT_ID}
  namespaceSelector:
    matchNames:
      - tenant-${TENANT_ID}
  endpoints:
    - port: metrics
      interval: 15s
      path: /metrics
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: tenant-${TENANT_ID}-rules
  namespace: monitoring
  labels:
    app: tenant-${TENANT_ID}
    release: prometheus
spec:
  groups:
  - name: tenant.${TENANT_ID}.rules
    rules:
    - alert: TenantHighCPUUsage
      expr: sum(rate(container_cpu_usage_seconds_total{namespace="tenant-${TENANT_ID}"}[5m])) by (pod) > 0.8
      for: 5m
      labels:
        severity: warning
        tenant: ${TENANT_ID}
      annotations:
        summary: "High CPU usage for tenant ${TENANT_ID}"
        description: "Pod {{ $labels.pod }} in namespace tenant-${TENANT_ID} has high CPU usage"
    
    - alert: TenantHighMemoryUsage
      expr: sum(container_memory_working_set_bytes{namespace="tenant-${TENANT_ID}"}) by (pod) / sum(container_spec_memory_limit_bytes{namespace="tenant-${TENANT_ID}"}) by (pod) > 0.8
      for: 5m
      labels:
        severity: warning
        tenant: ${TENANT_ID}
      annotations:
        summary: "High memory usage for tenant ${TENANT_ID}"
        description: "Pod {{ $labels.pod }} in namespace tenant-${TENANT_ID} has high memory usage"
    
    - alert: TenantHighDiskUsage
      expr: sum(kubelet_volume_stats_used_bytes{namespace="tenant-${TENANT_ID}"}) by (persistentvolumeclaim) / sum(kubelet_volume_stats_capacity_bytes{namespace="tenant-${TENANT_ID}"}) by (persistentvolumeclaim) > 0.8
      for: 5m
      labels:
        severity: warning
        tenant: ${TENANT_ID}
      annotations:
        summary: "High disk usage for tenant ${TENANT_ID}"
        description: "PVC {{ $labels.persistentvolumeclaim }} in namespace tenant-${TENANT_ID} has high disk usage"
    
    - alert: TenantHighNetworkUsage
      expr: sum(rate(container_network_transmit_bytes_total{namespace="tenant-${TENANT_ID}"}[5m])) + sum(rate(container_network_receive_bytes_total{namespace="tenant-${TENANT_ID}"}[5m])) > 100000000
      for: 5m
      labels:
        severity: warning
        tenant: ${TENANT_ID}
      annotations:
        summary: "High network usage for tenant ${TENANT_ID}"
        description: "Namespace tenant-${TENANT_ID} has high network usage"
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: autotest2029-backend-monitor
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: autotest2029-backend
  namespaceSelector:
    matchNames:
      - tenant-autotest2029
  endpoints:
    - port: http
      targetPort: 8080
      interval: 15s
      path: /metrics
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: newtest2025-backend-monitor
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: newtest2025-backend
  namespaceSelector:
    matchNames:
      - tenant-newtest2025
  endpoints:
    - port: http
      targetPort: 8080
      interval: 15s
      path: /metrics
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: newtest2027-backend-monitor
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: newtest2027-backend
  namespaceSelector:
    matchNames:
      - tenant-newtest2027
  endpoints:
    - port: http
      targetPort: 8080
      interval: 15s
      path: /metrics
