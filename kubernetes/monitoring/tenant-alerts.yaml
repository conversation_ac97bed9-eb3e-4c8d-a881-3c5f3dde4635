apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: tenant-alerts
  namespace: monitoring
  labels:
    prometheus: k8s
    role: alert-rules
spec:
  groups:
  - name: tenant.rules
    rules:
    - alert: TenantHighCPUUsage
      expr: sum(rate(container_cpu_usage_seconds_total{namespace=~"tenant-.*"}[5m])) by (namespace) / sum(kube_pod_container_resource_limits_cpu_cores{namespace=~"tenant-.*"}) by (namespace) > 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High CPU usage for tenant {{ $labels.namespace }}"
        description: "Tenant {{ $labels.namespace }} has high CPU usage (> 80%) for more than 5 minutes."
    
    - alert: TenantHighMemoryUsage
      expr: sum(container_memory_usage_bytes{namespace=~"tenant-.*"}) by (namespace) / sum(kube_pod_container_resource_limits_memory_bytes{namespace=~"tenant-.*"}) by (namespace) > 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High memory usage for tenant {{ $labels.namespace }}"
        description: "Tenant {{ $labels.namespace }} has high memory usage (> 80%) for more than 5 minutes."
    
    - alert: TenantHighDiskUsage
      expr: sum(kubelet_volume_stats_used_bytes{namespace=~"tenant-.*"}) by (namespace, persistentvolumeclaim) / sum(kubelet_volume_stats_capacity_bytes{namespace=~"tenant-.*"}) by (namespace, persistentvolumeclaim) > 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High disk usage for tenant {{ $labels.namespace }}"
        description: "Tenant {{ $labels.namespace }} has high disk usage (> 80%) for PVC {{ $labels.persistentvolumeclaim }} for more than 5 minutes."
    
    - alert: TenantHighAPILatency
      expr: histogram_quantile(0.95, sum(rate(istio_request_duration_milliseconds_bucket{destination_service_namespace=~"tenant-.*"}[5m])) by (destination_service_namespace, destination_service_name, le)) > 500
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High API latency for tenant service {{ $labels.destination_service_name }} in {{ $labels.destination_service_namespace }}"
        description: "95th percentile latency for service {{ $labels.destination_service_name }} in {{ $labels.destination_service_namespace }} is above 500ms for more than 5 minutes."
    
    - alert: TenantHighErrorRate
      expr: sum(rate(istio_requests_total{destination_service_namespace=~"tenant-.*", response_code=~"5.*"}[5m])) by (destination_service_namespace, destination_service_name) / sum(rate(istio_requests_total{destination_service_namespace=~"tenant-.*"}[5m])) by (destination_service_namespace, destination_service_name) > 0.05
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High error rate for tenant service {{ $labels.destination_service_name }} in {{ $labels.destination_service_namespace }}"
        description: "Error rate for service {{ $labels.destination_service_name }} in {{ $labels.destination_service_namespace }} is above 5% for more than 5 minutes."
    
    - alert: TenantDatabaseHighCPU
      expr: aws_rds_cpu_utilization_average{dbinstance_identifier=~".*tenant.*"} > 80
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High CPU usage for tenant database {{ $labels.dbinstance_identifier }}"
        description: "Database {{ $labels.dbinstance_identifier }} has high CPU usage (> 80%) for more than 5 minutes."
    
    - alert: TenantDatabaseHighMemory
      expr: aws_rds_freeable_memory_average{dbinstance_identifier=~".*tenant.*"} / aws_rds_database_memory_average{dbinstance_identifier=~".*tenant.*"} < 0.2
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Low free memory for tenant database {{ $labels.dbinstance_identifier }}"
        description: "Database {{ $labels.dbinstance_identifier }} has low free memory (< 20%) for more than 5 minutes."
    
    - alert: TenantDatabaseHighConnections
      expr: aws_rds_database_connections_average{dbinstance_identifier=~".*tenant.*"} / aws_rds_max_database_connections_average{dbinstance_identifier=~".*tenant.*"} > 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High connection count for tenant database {{ $labels.dbinstance_identifier }}"
        description: "Database {{ $labels.dbinstance_identifier }} has high connection count (> 80% of max) for more than 5 minutes."
