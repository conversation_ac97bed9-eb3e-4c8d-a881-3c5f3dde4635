# PRODUCTION KARPENTER NODEPOOLS - High Capacity for Auto-scaling
# This prevents the resource limitation issues by providing ample node capacity

# ===== GENERAL WORKLOADS NODEPOOL (UPGRADED) =====
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: general-workloads-production
  labels:
    app.kubernetes.io/managed-by: terraform
    environment: production
    nodepool-tier: production
spec:
  template:
    metadata:
      labels:
        provisioning-group: general
        environment: production
        node-type: general-workloads
        scaling-tier: production
    spec:
      requirements:
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand", "spot"]  # Mix of on-demand and spot
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            # Cost-effective instances
            - "t3a.medium"    # 2 vCPU, 4GB RAM
            - "t3a.large"     # 2 vCPU, 8GB RAM
            - "t3a.xlarge"    # 4 vCPU, 16GB RAM
            - "t3a.2xlarge"   # 8 vCPU, 32GB RAM
            # General purpose instances
            - "m5.large"      # 2 vCPU, 8GB RAM
            - "m5.xlarge"     # 4 vCPU, 16GB RAM
            - "m5.2xlarge"    # 8 vCPU, 32GB RAM
            # CPU optimized for queue processing
            - "c5.large"      # 2 vCPU, 4GB RAM
            - "c5.xlarge"     # 4 vCPU, 8GB RAM
            - "c5.2xlarge"    # 8 vCPU, 16GB RAM
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: general-workloads
  
  # MASSIVELY INCREASED LIMITS (was 100 CPU)
  limits:
    cpu: 300           # 3x increase for general workloads
    memory: 600Gi      # 3x increase for general workloads
  
  disruption:
    consolidationPolicy: WhenEmpty
    consolidateAfter: 30s

---
# ===== TENANT WORKLOADS NODEPOOL (MASSIVELY UPGRADED) =====
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: tenant-workloads-production
  labels:
    app.kubernetes.io/managed-by: terraform
    environment: production
    nodepool-tier: production
    workload-type: tenant
spec:
  template:
    metadata:
      labels:
        workload-type: tenant
        environment: production
        node-type: tenant-workloads
        scaling-tier: production-high-capacity
    spec:
      requirements:
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot", "on-demand"]  # Prefer spot for cost savings
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            # Standard tenant instances
            - "t3a.large"     # 2 vCPU, 8GB RAM - current standard
            - "t3a.xlarge"    # 4 vCPU, 16GB RAM - for scaling up
            - "t3a.2xlarge"   # 8 vCPU, 32GB RAM - for peak loads
            # High-memory instances for data processing
            - "m5.large"      # 2 vCPU, 8GB RAM
            - "m5.xlarge"     # 4 vCPU, 16GB RAM
            - "m5.2xlarge"    # 8 vCPU, 32GB RAM
            - "m5.4xlarge"    # 16 vCPU, 64GB RAM - for high-load tenants
            # CPU-optimized for queue workers
            - "c5.large"      # 2 vCPU, 4GB RAM
            - "c5.xlarge"     # 4 vCPU, 8GB RAM
            - "c5.2xlarge"    # 8 vCPU, 16GB RAM
            - "c5.4xlarge"    # 16 vCPU, 32GB RAM
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: tenant-workloads
      taints:
        - key: workload-type
          value: tenant
          effect: NoSchedule
  
  # MASSIVE CAPACITY INCREASE (was 50 CPU)
  limits:
    cpu: 500           # 10x increase from 50 CPU!
    memory: 1000Gi     # 10x increase from 100Gi!
  
  disruption:
    consolidationPolicy: WhenUnderutilized  # More aggressive cost optimization
    consolidateAfter: 30s

---
# ===== HIGH-PERFORMANCE NODEPOOL (NEW) =====
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: high-performance-workloads
  labels:
    app.kubernetes.io/managed-by: terraform
    environment: production
    nodepool-tier: high-performance
spec:
  template:
    metadata:
      labels:
        performance-tier: high
        environment: production
        node-type: high-performance
        scaling-tier: burst-capacity
    spec:
      requirements:
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]  # On-demand for consistent performance
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            # High-performance instances for burst capacity
            - "m5.4xlarge"    # 16 vCPU, 64GB RAM
            - "m5.8xlarge"    # 32 vCPU, 128GB RAM
            - "c5.4xlarge"    # 16 vCPU, 32GB RAM
            - "c5.9xlarge"    # 36 vCPU, 72GB RAM
            - "r5.2xlarge"    # 8 vCPU, 64GB RAM - memory optimized
            - "r5.4xlarge"    # 16 vCPU, 128GB RAM - memory optimized
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: high-performance-workloads
      taints:
        - key: performance-tier
          value: high
          effect: NoSchedule
  
  # HIGH-CAPACITY LIMITS
  limits:
    cpu: 200           # For burst workloads
    memory: 400Gi      # Memory-intensive processing
  
  disruption:
    consolidationPolicy: WhenEmpty
    consolidateAfter: 60s    # Longer consolidation time for expensive instances

---
# ===== EC2NODECLASS FOR GENERAL WORKLOADS =====
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: general-workloads
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  amiFamily: AL2
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: "production-eks"
        kubernetes.io/role/internal-elb: "1"
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: "production-eks"
  role: "KarpenterNodeInstanceProfile"
  
  userData: |
    #!/bin/bash
    /etc/eks/bootstrap.sh production-eks
    
    # Configure kubelet for high pod density
    echo "maxPods: 110" >> /etc/kubernetes/kubelet/kubelet-config.json
    echo "clusterDNS: ['*************']" >> /etc/kubernetes/kubelet/kubelet-config.json
    
    # Install monitoring and optimization tools
    yum update -y
    yum install -y amazon-cloudwatch-agent htop iotop

  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 80Gi      # Increased storage
        volumeType: gp3
        iops: 4000           # Higher IOPS
        throughput: 250      # Higher throughput
        encrypted: true
        deleteOnTermination: true

  tags:
    Name: general-karpenter-node
    Environment: production
    ManagedBy: karpenter
    NodePool: general-workloads
    CostCenter: shared-infrastructure

---
# ===== EC2NODECLASS FOR TENANT WORKLOADS =====
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: tenant-workloads
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  amiFamily: AL2
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: "production-eks"
        kubernetes.io/role/internal-elb: "1"
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: "production-eks"
  role: "KarpenterNodeInstanceProfile"
  
  userData: |
    #!/bin/bash
    /etc/eks/bootstrap.sh production-eks
    
    # Configure kubelet for tenant workloads
    echo "maxPods: 110" >> /etc/kubernetes/kubelet/kubelet-config.json
    echo "clusterDNS: ['*************']" >> /etc/kubernetes/kubelet/kubelet-config.json
    
    # Install tenant-specific monitoring
    yum update -y
    yum install -y amazon-cloudwatch-agent
    
    # Configure log rotation for tenant logs
    echo "/var/log/pods/*.log {
        daily
        missingok
        rotate 7
        compress
        notifempty
        create 0644 root root
    }" > /etc/logrotate.d/kubernetes-pods
    
    # Label node for tenant workloads
    kubectl label node $(hostname) workload-type=tenant --overwrite || true

  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 100Gi     # Larger storage for tenant data
        volumeType: gp3
        iops: 5000           # High IOPS for database workloads
        throughput: 300      # High throughput
        encrypted: true
        deleteOnTermination: true

  tags:
    Name: tenant-karpenter-node
    Environment: production
    ManagedBy: karpenter
    NodePool: tenant-workloads
    CostCenter: tenant-infrastructure
    WorkloadType: tenant

---
# ===== EC2NODECLASS FOR HIGH-PERFORMANCE WORKLOADS =====
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: high-performance-workloads
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  amiFamily: AL2
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: "production-eks"
        kubernetes.io/role/internal-elb: "1"
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: "production-eks"
  role: "KarpenterNodeInstanceProfile"
  
  userData: |
    #!/bin/bash
    /etc/eks/bootstrap.sh production-eks
    
    # Configure kubelet for high-performance workloads
    echo "maxPods: 110" >> /etc/kubernetes/kubelet/kubelet-config.json
    echo "systemReserved: { 'cpu': '500m', 'memory': '1Gi' }" >> /etc/kubernetes/kubelet/kubelet-config.json
    echo "kubeReserved: { 'cpu': '500m', 'memory': '1Gi' }" >> /etc/kubernetes/kubelet/kubelet-config.json
    
    # Performance optimizations
    echo 'vm.swappiness = 1' >> /etc/sysctl.conf
    echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
    echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
    sysctl -p

  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 200Gi     # Large storage for high-performance workloads
        volumeType: gp3
        iops: 10000          # Maximum IOPS
        throughput: 500      # Maximum throughput
        encrypted: true
        deleteOnTermination: true

  tags:
    Name: high-performance-karpenter-node
    Environment: production
    ManagedBy: karpenter
    NodePool: high-performance-workloads
    CostCenter: high-performance-infrastructure
    PerformanceTier: high