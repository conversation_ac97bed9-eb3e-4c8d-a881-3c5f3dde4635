# Enhanced KEDA ScaledObjects for Comprehensive Tenant Scaling
# Supports multiple scaling triggers for different tenant workload patterns

apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-webapp-cpu-scaler
  namespace: default
  labels:
    app: tenant-scaling
    scaler-type: cpu-memory
spec:
  scaleTargetRef:
    name: karpenter-test  # Replace with actual tenant webapp deployment
  minReplicaCount: 1
  maxReplicaCount: 25
  pollingInterval: 15    # Check every 15 seconds
  cooldownPeriod: 60     # Cool down for 1 minute after scaling
  triggers:
    # CPU-based scaling
    - type: cpu
      metricType: Utilization
      metadata:
        value: '60'  # Scale when CPU > 60%
    
    # Memory-based scaling  
    - type: memory
      metricType: Utilization
      metadata:
        value: '70'  # Scale when memory > 70%

---

apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-queue-processor-scaler
  namespace: default
  labels:
    app: tenant-scaling
    scaler-type: queue-based
spec:
  scaleTargetRef:
    name: karpenter-test  # Replace with queue processor deployment
  minReplicaCount: 0     # Scale to zero when no work
  maxReplicaCount: 50    # Support burst processing
  pollingInterval: 10    # Check every 10 seconds
  cooldownPeriod: 30     # Quick cooldown for queue processing
  triggers:
    # Prometheus-based queue depth scaling
    - type: prometheus
      metadata:
        serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
        metricName: tenant_queue_depth
        threshold: '5'
        query: |
          sum(
            rabbitmq_queue_messages{queue=~"tenant-.*-queue"}
          ) by (queue) or vector(0)

---

apiVersion: keda.sh/v1alpha1  
kind: ScaledObject
metadata:
  name: tenant-database-scaler
  namespace: default
  labels:
    app: tenant-scaling
    scaler-type: database-metrics
spec:
  scaleTargetRef:
    name: karpenter-test  # Replace with database worker deployment
  minReplicaCount: 1
  maxReplicaCount: 20
  pollingInterval: 30    # Check every 30 seconds (less frequent for DB)
  cooldownPeriod: 120    # Longer cooldown for database operations
  triggers:
    # Database connection scaling
    - type: prometheus
      metadata:
        serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
        metricName: database_connections_ratio
        threshold: '0.7'
        query: |
          sum(mysql_global_status_threads_connected) / 
          sum(mysql_global_variables_max_connections) or vector(0)
    
    # Database query latency scaling
    - type: prometheus
      metadata:
        serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
        metricName: database_query_latency
        threshold: '500'  # 500ms threshold
        query: |
          histogram_quantile(0.95,
            rate(mysql_perf_schema_events_statements_summary_by_digest_sum_timer_wait[5m])
          ) or vector(0)

---

apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-http-requests-scaler
  namespace: default
  labels:
    app: tenant-scaling
    scaler-type: http-traffic
spec:
  scaleTargetRef:
    name: karpenter-test  # Replace with web application deployment
  minReplicaCount: 2     # Always keep minimum for availability
  maxReplicaCount: 30
  pollingInterval: 10
  cooldownPeriod: 60
  triggers:
    # HTTP request rate scaling
    - type: prometheus
      metadata:
        serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
        metricName: http_requests_per_second
        threshold: '100'  # Scale when > 100 RPS per instance
        query: |
          sum(
            rate(nginx_ingress_controller_requests[1m])
          ) by (ingress) or vector(0)
    
    # HTTP error rate scaling (scale up on errors)
    - type: prometheus
      metadata:
        serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
        metricName: http_error_rate
        threshold: '0.05'  # Scale when error rate > 5%
        query: |
          sum(rate(nginx_ingress_controller_requests{status=~"5.."}[1m])) /
          sum(rate(nginx_ingress_controller_requests[1m])) or vector(0)

---

apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-batch-processor-scaler
  namespace: default
  labels:
    app: tenant-scaling
    scaler-type: batch-processing
spec:
  scaleTargetRef:
    name: karpenter-test  # Replace with batch processor deployment
  minReplicaCount: 0     # Scale to zero when no batch jobs
  maxReplicaCount: 100   # High burst capacity for batch processing
  pollingInterval: 20
  cooldownPeriod: 300    # 5-minute cooldown for batch jobs
  triggers:
    # Kubernetes Job-based scaling
    - type: kubernetes-workload
      metadata:
        podSelector: 'app=tenant-batch-job,phase=Pending'
        value: '1'  # Scale up for each pending job
    
    # Cron-based pre-scaling (scale up before known batch windows)
    - type: cron
      metadata:
        timezone: Europe/Berlin
        start: '0 8 * * *'    # Scale up at 8 AM
        end: '0 18 * * *'     # Scale down at 6 PM
        desiredReplicas: '5'  # Maintain 5 replicas during business hours

---

apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-monitoring-scaler
  namespace: monitoring
  labels:
    app: tenant-scaling
    scaler-type: monitoring
spec:
  scaleTargetRef:
    name: prometheus-server  # Scale Prometheus for increased tenant load
  minReplicaCount: 1
  maxReplicaCount: 5
  pollingInterval: 60      # Check every minute
  cooldownPeriod: 300      # 5-minute cooldown
  triggers:
    # Prometheus memory usage
    - type: prometheus
      metadata:
        serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
        metricName: prometheus_memory_usage
        threshold: '0.8'  # Scale when memory usage > 80%
        query: |
          sum(container_memory_working_set_bytes{pod=~"prometheus-server-.*"}) /
          sum(container_spec_memory_limit_bytes{pod=~"prometheus-server-.*"}) or vector(0)
    
    # Query load scaling
    - type: prometheus
      metadata:
        serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
        metricName: prometheus_query_rate
        threshold: '100'  # Scale when > 100 queries/sec
        query: |
          rate(prometheus_engine_queries[5m]) or vector(0)

---

apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-file-processor-scaler
  namespace: default
  labels:
    app: tenant-scaling
    scaler-type: file-processing
spec:
  scaleTargetRef:
    name: karpenter-test  # Replace with file processor deployment
  minReplicaCount: 0
  maxReplicaCount: 25
  pollingInterval: 30
  cooldownPeriod: 120
  triggers:
    # AWS S3 queue depth (SQS integration)
    - type: aws-sqs-queue
      authenticationRef:
        name: aws-credentials  # Requires AWS credentials
      metadata:
        queueURL: "https://sqs.eu-central-1.amazonaws.com/ACCOUNT/tenant-file-processing-queue"
        queueLength: "10"
        awsRegion: "eu-central-1"
    
    # File processing backlog (Prometheus)
    - type: prometheus
      metadata:
        serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
        metricName: file_processing_backlog
        threshold: '20'
        query: |
          sum(
            increase(files_uploaded_total[5m]) - increase(files_processed_total[5m])
          ) or vector(0)

---

# TriggerAuthentication for AWS services
apiVersion: keda.sh/v1alpha1
kind: TriggerAuthentication
metadata:
  name: aws-credentials
  namespace: default
spec:
  podIdentity:
    provider: aws-eks  # Use EKS pod identity
  secretTargetRef:
    - parameter: awsAccessKeyID
      name: aws-secret
      key: access-key-id
    - parameter: awsSecretAccessKey  
      name: aws-secret
      key: secret-access-key

---

# Secret for AWS credentials (if needed)
apiVersion: v1
kind: Secret
metadata:
  name: aws-secret
  namespace: default
type: Opaque
data:
  # Base64 encoded values - replace with actual credentials if not using IAM roles
  access-key-id: ""     # Leave empty if using IAM roles for service accounts
  secret-access-key: "" # Leave empty if using IAM roles for service accounts