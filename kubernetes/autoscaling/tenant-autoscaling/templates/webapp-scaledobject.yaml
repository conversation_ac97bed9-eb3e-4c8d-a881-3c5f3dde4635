{{- if .Values.webapp.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Release.Name }}-webapp
  namespace: {{ .Release.Namespace }}
spec:
  scaleTargetRef:
    name: {{ include "tenant.name" . }}
  minReplicaCount: {{ .Values.webapp.min | default 1 }}
  maxReplicaCount: {{ .Values.webapp.max | default 5 }}
  pollingInterval: 30
  cooldownPeriod: 300
  triggers:
  - type: cpu
    metadata:
      type: Utilization
      value: "{{ .Values.webapp.cpuUtilization | default 70 }}"
{{- end }}