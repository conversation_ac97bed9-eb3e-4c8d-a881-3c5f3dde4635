{{- if and .Values.queues.enabled .Values.queues.default.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Release.Name }}-default-queue
  namespace: {{ .Release.Namespace }}
spec:
  scaleTargetRef:
    name: {{ default (printf "%s-default-queue" (include "tenant.queueName" .)) .Values.queues.default.deploymentName }}
  minReplicaCount: {{ .Values.queues.default.min | default 1 }}
  maxReplicaCount: {{ .Values.queues.default.max | default 10 }}
  triggers:
  - type: rabbitmq
    metadata:
      protocol: {{ .Values.queues.default.protocol | default "amqp" | quote }}
      mode: {{ .Values.queues.default.mode | default "QueueLength" | quote }}
      value: {{ .Values.queues.default.pendingMessages | default 5 | quote }}
      queueName: {{ .Values.queues.default.queueName | default "default_worker_queue" | quote }}
      hostFromEnv: {{ .Values.queues.default.hostEnv | default "RABBITMQ_HOST" | quote }}
      portFromEnv: {{ .Values.queues.default.portEnv | default "RABBITMQ_PORT" | quote }}
      usernameFromEnv: {{ .Values.queues.default.userEnv | default "RABBITMQ_USER" | quote }}
      passwordFromEnv: {{ .Values.queues.default.passwordEnv | default "RABBITMQ_PASSWORD" | quote }}
---
{{- end }}

{{- if and .Values.queues.enabled .Values.queues.folder.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Release.Name }}-folder-queue
  namespace: {{ .Release.Namespace }}
spec:
  scaleTargetRef:
    name: {{ default (printf "%s-folder-queue" (include "tenant.queueName" .)) .Values.queues.folder.deploymentName }}
  minReplicaCount: {{ .Values.queues.folder.min | default 1 }}
  maxReplicaCount: {{ .Values.queues.folder.max | default 10 }}
  triggers:
  - type: rabbitmq
    metadata:
      protocol: {{ .Values.queues.default.protocol | default "amqp" | quote }}
      mode: {{ .Values.queues.default.mode | default "QueueLength" | quote }}
      value: {{ .Values.queues.folder.pendingMessages | default 5 | quote }}
      queueName: {{ .Values.queues.folder.queueName | default "folder_worker_queue" | quote }}
      hostFromEnv: {{ .Values.queues.default.hostEnv | default "RABBITMQ_HOST" | quote }}
      portFromEnv: {{ .Values.queues.default.portEnv | default "RABBITMQ_PORT" | quote }}
      usernameFromEnv: {{ .Values.queues.default.userEnv | default "RABBITMQ_USER" | quote }}
      passwordFromEnv: {{ .Values.queues.default.passwordEnv | default "RABBITMQ_PASSWORD" | quote }}
---
{{- end }}

{{- if and .Values.queues.enabled .Values.queues.notification.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Release.Name }}-notification-queue
  namespace: {{ .Release.Namespace }}
spec:
  scaleTargetRef:
    name: {{ default (printf "%s-notification-queue" (include "tenant.queueName" .)) .Values.queues.notification.deploymentName }}
  minReplicaCount: {{ .Values.queues.notification.min | default 1 }}
  maxReplicaCount: {{ .Values.queues.notification.max | default 10 }}
  triggers:
  - type: rabbitmq
    metadata:
      protocol: {{ .Values.queues.default.protocol | default "amqp" | quote }}
      mode: {{ .Values.queues.default.mode | default "QueueLength" | quote }}
      value: {{ .Values.queues.notification.pendingMessages | default 5 | quote }}
      queueName: {{ .Values.queues.notification.queueName | default "notification_worker_queue" | quote }}
      hostFromEnv: {{ .Values.queues.default.hostEnv | default "RABBITMQ_HOST" | quote }}
      portFromEnv: {{ .Values.queues.default.portEnv | default "RABBITMQ_PORT" | quote }}
      usernameFromEnv: {{ .Values.queues.default.userEnv | default "RABBITMQ_USER" | quote }}
      passwordFromEnv: {{ .Values.queues.default.passwordEnv | default "RABBITMQ_PASSWORD" | quote }}
{{- end }}