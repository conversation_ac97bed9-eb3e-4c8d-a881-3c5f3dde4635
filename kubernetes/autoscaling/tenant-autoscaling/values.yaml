# Global toggles
webapp:
  enabled: true
  # If not set, defaults to "<tenant>-webapp" where <tenant> is namespace without "tenant-"
  deploymentName: ""
  min: 1
  max: 5
  cpuUtilization: 70

queues:
  enabled: false
  # RabbitMQ ScaledObject examples (disabled by default)
  default:
    enabled: false
    deploymentName: ""
    queueName: default_worker_queue
    hostEnv: RABBITMQ_HOST
    portEnv: RABBITMQ_PORT
    userEnv: RABBITMQ_USER
    passwordEnv: RABBITMQ_PASSWORD
    protocol: amqp
    mode: QueueLength
    # Scale to 1 per 5 messages pending
    pendingMessages: 5
    min: 1
    max: 10
  folder:
    enabled: false
    deploymentName: ""
    queueName: folder_worker_queue
    pendingMessages: 5
    min: 1
    max: 10
  notification:
    enabled: false
    deploymentName: ""
    queueName: notification_worker_queue
    pendingMessages: 5
    min: 1
    max: 10