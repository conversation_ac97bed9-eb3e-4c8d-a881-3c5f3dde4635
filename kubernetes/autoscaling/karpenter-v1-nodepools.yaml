# Updated Karpenter v1 API NodePools and EC2NodeClasses
# Replaces old v1alpha5 Provisioner configurations

# General NodePool for all workloads
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: general-workloads
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  template:
    metadata:
      labels:
        provisioning-group: general
        environment: production
        node-type: general-workloads
    spec:
      requirements:
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand", "spot"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - "t3.small"
            - "t3.medium"
            - "t3.large"
            - "t3a.small"
            - "t3a.medium"
            - "t3a.large"
            - "m5.large"
            - "m5a.large"
            - "c5.large"
            - "c5a.large"
      nodeClassRef:
        apiVersion: karpenter.k8s.aws/v1
        kind: EC2NodeClass
        name: general-workloads
  limits:
    cpu: 100
    memory: 200Gi
  disruption:
    consolidationPolicy: WhenEmpty
    consolidateAfter: 30s
    expireAfter: 30m

---

# Tenant-specific NodePool with taints
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: tenant-workloads
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  template:
    metadata:
      labels:
        workload-type: tenant
        environment: production
        node-type: tenant-workloads
    spec:
      requirements:
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot", "on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - "t3.small"
            - "t3.medium"
            - "t3a.small"
            - "t3a.medium"
      nodeClassRef:
        apiVersion: karpenter.k8s.aws/v1
        kind: EC2NodeClass
        name: tenant-workloads
      taints:
        - key: workload-type
          value: tenant
          effect: NoSchedule
  limits:
    cpu: 50
    memory: 100Gi
  disruption:
    consolidationPolicy: WhenEmpty
    consolidateAfter: 30s
    expireAfter: 30m

---

# General EC2NodeClass
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: general-workloads
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  amiSelectorTerms:
    - alias: "al2023@latest"
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: "production-wks"
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: "production-wks"
  instanceStorePolicy: RAID0
  userData: |
    #!/bin/bash
    /etc/eks/bootstrap.sh production-wks
    # Optimize for general workloads
    echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
    echo 'fs.file-max=2097152' >> /etc/sysctl.conf
    echo 'net.core.somaxconn=65535' >> /etc/sysctl.conf
    sysctl -p
  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 50Gi
        volumeType: gp3
        iops: 3000
        throughput: 125
        deleteOnTermination: true
        encrypted: true
  tags:
    environment: production
    provisioner: karpenter
    cluster: production-wks
    node-type: general-workloads

---

# Tenant EC2NodeClass
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: tenant-workloads
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  amiSelectorTerms:
    - alias: "al2023@latest"
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: "production-wks"
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: "production-wks"
  instanceStorePolicy: RAID0
  userData: |
    #!/bin/bash
    /etc/eks/bootstrap.sh production-wks
    # Optimize for tenant workloads
    echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
    echo 'fs.file-max=2097152' >> /etc/sysctl.conf
    echo 'net.core.somaxconn=65535' >> /etc/sysctl.conf
    # Tenant-specific optimizations
    echo 'kernel.pid_max=4194304' >> /etc/sysctl.conf
    echo 'net.ipv4.ip_local_port_range = 1024 65535' >> /etc/sysctl.conf
    sysctl -p
  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 30Gi
        volumeType: gp3
        iops: 3000
        throughput: 125
        deleteOnTermination: true
        encrypted: true
  tags:
    environment: production
    provisioner: karpenter
    cluster: production-wks
    node-type: tenant-workloads
    workload-type: tenant