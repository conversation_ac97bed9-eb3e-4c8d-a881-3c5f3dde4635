# Optimized Karpenter NodePools for 100+ Tenant Scaling
# This configuration supports unlimited tenant onboarding with cost optimization

apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: general-workloads-optimized
  labels:
    app.kubernetes.io/managed-by: terraform
    scaling-tier: general
spec:
  # Disruption settings for cost optimization
  disruption:
    budgets:
      - nodes: "10%"
    consolidateAfter: 30s
    consolidationPolicy: WhenEmpty
  
  # OPTIMIZED: Increased limits for 100+ tenants
  limits:
    cpu: 2000        # Up from 100
    memory: 4000Gi   # Up from 200Gi
  
  template:
    metadata:
      labels:
        Environment: production
        Node-Type: general-workloads
        Provisioning-Group: general
        Cost-Optimization: enabled
    
    spec:
      expireAfter: 720h  # 30 days
      
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: general-workloads
      
      requirements:
        # Architecture requirement
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        
        # OS requirement  
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        
        # Capacity type (spot + on-demand for reliability)
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot", "on-demand"]
        
        # OPTIMIZED: Expanded instance types for different workload sizes
        - key: node.kubernetes.io/instance-type
          operator: In
          values: 
            - "t3.medium"    # 2 vCPU, 4GB - Small workloads
            - "t3.large"     # 2 vCPU, 8GB - Medium workloads  
            - "t3.xlarge"    # 4 vCPU, 16GB - Large workloads
            - "m5.large"     # 2 vCPU, 8GB - General purpose
            - "m5.xlarge"    # 4 vCPU, 16GB - General purpose
            - "m5.2xlarge"   # 8 vCPU, 32GB - High performance
            - "c5.large"     # 2 vCPU, 4GB - CPU optimized
            - "c5.xlarge"    # 4 vCPU, 8GB - CPU optimized

---

apiVersion: karpenter.sh/v1
kind: NodePool 
metadata:
  name: tenant-workloads-optimized
  labels:
    app.kubernetes.io/managed-by: terraform
    scaling-tier: tenant
spec:
  # Disruption settings for faster tenant scaling
  disruption:
    budgets:
      - nodes: "50%"  # Allow more aggressive scaling for tenant workloads
    consolidateAfter: 15s  # Faster consolidation
    consolidationPolicy: WhenEmpty
  
  # OPTIMIZED: Higher limits for tenant workloads  
  limits:
    cpu: 3000        # Up from previous limits
    memory: 6000Gi   # Up from previous limits
  
  template:
    metadata:
      labels:
        Environment: production
        Node-Type: tenant-workloads
        Provisioning-Group: tenant
        Cost-Optimization: aggressive
        Tenant-Dedicated: "true"
    
    spec:
      expireAfter: 168h  # 7 days (shorter for tenant workloads)
      
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass  
        name: tenant-workloads
      
      # Taints to ensure only tenant workloads are scheduled here
      taints:
        - key: tenant-workload
          value: "true"
          effect: NoSchedule
        - key: dedicated
          value: tenant
          effect: NoSchedule
      
      requirements:
        # Architecture requirement
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        
        # OS requirement
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        
        # OPTIMIZED: Prefer spot instances for cost savings
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot", "on-demand"]
        
        # OPTIMIZED: Larger instance types for tenant workloads
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - "t3.large"     # 2 vCPU, 8GB - Small tenants
            - "t3.xlarge"    # 4 vCPU, 16GB - Medium tenants
            - "t3.2xlarge"   # 8 vCPU, 32GB - Large tenants
            - "m5.xlarge"    # 4 vCPU, 16GB - General purpose
            - "m5.2xlarge"   # 8 vCPU, 32GB - High performance
            - "m5.4xlarge"   # 16 vCPU, 64GB - Very large tenants
            - "c5.xlarge"    # 4 vCPU, 8GB - CPU intensive
            - "c5.2xlarge"   # 8 vCPU, 16GB - CPU intensive
            - "c5.4xlarge"   # 16 vCPU, 32GB - Very CPU intensive

---

apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: priority-tenants
  labels:
    app.kubernetes.io/managed-by: terraform
    scaling-tier: priority
spec:
  # Disruption settings for high-priority workloads
  disruption:
    budgets:
      - nodes: "20%"  # Conservative disruption for priority workloads
    consolidateAfter: 60s
    consolidationPolicy: WhenEmpty
  
  # OPTIMIZED: Dedicated resources for priority tenants
  limits:
    cpu: 1000
    memory: 2000Gi
  
  template:
    metadata:
      labels:
        Environment: production
        Node-Type: priority-tenants
        Provisioning-Group: priority
        Cost-Optimization: performance
        Tenant-Priority: high
    
    spec:
      expireAfter: 720h  # 30 days (longer for priority)
      
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: general-workloads  # Reuse general nodeclass
      
      # Taints for priority tenant isolation
      taints:
        - key: priority-tenant
          value: "true" 
          effect: NoSchedule
        - key: dedicated
          value: priority
          effect: NoSchedule
      
      requirements:
        # Architecture requirement
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        
        # OS requirement
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        
        # PRIORITY: On-demand only for reliability
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        
        # OPTIMIZED: High-performance instance types only
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - "m5.2xlarge"   # 8 vCPU, 32GB
            - "m5.4xlarge"   # 16 vCPU, 64GB
            - "c5.2xlarge"   # 8 vCPU, 16GB - CPU intensive
            - "c5.4xlarge"   # 16 vCPU, 32GB - CPU intensive
            - "r5.2xlarge"   # 8 vCPU, 64GB - Memory optimized
            - "r5.4xlarge"   # 16 vCPU, 128GB - Memory optimized