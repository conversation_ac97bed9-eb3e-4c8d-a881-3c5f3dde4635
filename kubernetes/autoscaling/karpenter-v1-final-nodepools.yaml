# Final Karpenter v1 API NodePools (correctly formatted)

# General NodePool for all workloads
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: general-workloads
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  template:
    metadata:
      labels:
        provisioning-group: general
        environment: production
        node-type: general-workloads
    spec:
      requirements:
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand", "spot"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - "t3.small"
            - "t3.medium"
            - "t3.large"
            - "m5.large"
            - "m5.xlarge"
            - "c5.large"
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: general-workloads
  limits:
    cpu: 100
    memory: 200Gi
  disruption:
    consolidationPolicy: WhenEmpty
    consolidateAfter: 30s

---

# Tenant-specific NodePool with taints
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: tenant-workloads
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  template:
    metadata:
      labels:
        workload-type: tenant
        environment: production
        node-type: tenant-workloads
    spec:
      requirements:
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot", "on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - "t3.small"
            - "t3.medium"
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: tenant-workloads
      taints:
        - key: workload-type
          value: tenant
          effect: NoSchedule
  limits:
    cpu: 50
    memory: 100Gi
  disruption:
    consolidationPolicy: WhenEmpty
    consolidateAfter: 30s