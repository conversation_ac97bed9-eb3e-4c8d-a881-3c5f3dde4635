# Cluster-wide KEDA installation via <PERSON><PERSON> (no duplication with Karpenter)
# Safe defaults; install only when skip_k8s_connection is false.

variable "install_keda" {
  description = "Install KEDA via Helm"
  type        = bool
  default     = true
}

resource "helm_release" "keda" {
  count            = var.install_keda && !var.skip_k8s_connection ? 1 : 0
  name             = "keda"
  repository       = "https://kedacore.github.io/charts"
  chart            = "keda"
  version          = "2.12.1"
  namespace        = "keda"
  create_namespace = true
  provider         = helm.safe

  # Keep CRDs managed by <PERSON><PERSON> for simplicity here
  set { name = "podSecurityContext.enabled"; value = "true" }
  set { name = "serviceAccount.create"; value = "true" }
}