# EKS Cluster Configuration

# Data source for existing EKS cluster is defined in data.tf

# Create new EKS cluster or use existing one
resource "aws_eks_cluster" "this" {
  # Force creation of the EKS cluster
  count    = var.create_eks ? 1 : 0
  name     = var.cluster_name
  role_arn = var.create_iam_role ? aws_iam_role.cluster[0].arn : var.eks_cluster_iam_role_arn
  version  = "1.32"

  vpc_config {
    subnet_ids              = var.subnet_ids
    security_group_ids      = var.create_security_groups ? [aws_security_group.eks_cluster[0].id] : []
    endpoint_private_access = true
    endpoint_public_access  = var.endpoint_public_access

    # Restrict access to specific IPs if public access is enabled
    # Include both the current IP and the bastion host IP if available
    # If no specific CIDRs are provided, use the VPC CIDR
    # This is necessary for the bastion host to connect to the EKS cluster
    public_access_cidrs = var.endpoint_public_access ? (
      length(var.public_access_cidrs) > 0 ?
      var.public_access_cidrs :
      ["0.0.0.0/0"] # Use 0.0.0.0/0 as a fallback instead of VPC CIDR
    ) : []
  }

  # Enable EKS control plane logging
  enabled_cluster_log_types = [
    "api",
    "audit",
    "authenticator",
    "controllerManager",
    "scheduler"
  ]

  # Enable KMS encryption for EKS secrets
  dynamic "encryption_config" {
    for_each = var.kms_key_arn != "" ? [1] : []
    content {
      provider {
        key_arn = var.kms_key_arn
      }
      resources = ["secrets"]
    }
  }

  timeouts {
    create = "30m"
    delete = "30m"
  }

  # Ensure IAM Role is created before EKS Cluster
  depends_on = [
    aws_cloudwatch_log_group.eks_cluster,
    aws_iam_role_policy_attachment.cluster_AmazonEKSClusterPolicy,
    aws_iam_role_policy_attachment.cluster_AmazonEKSVPCResourceController
  ]

  # Add tags
  tags = merge(
    var.tags,
    {
      "Name"        = var.cluster_name,
      "Environment" = var.environment
    }
  )

  # Add lifecycle policy to handle existing resources
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = false
  }
}

# CloudWatch Log Group for EKS cluster logging
resource "aws_cloudwatch_log_group" "eks_cluster" {
  # Force creation of the CloudWatch Log Group
  count             = var.create_eks && var.create_eks_log_group ? 1 : 0
  name              = "/aws/eks/${var.cluster_name}/cluster"
  retention_in_days = var.log_retention_days
  # KMS keys have been disabled
  # kms_key_id        = var.kms_key_arn

  tags = merge(
    var.tags,
    {
      "Name"        = "${var.cluster_name}-logs",
      "Environment" = var.environment
    }
  )
}


# IAM Role for EKS Cluster
resource "aws_iam_role" "cluster" {
  count = var.create_iam_role ? 1 : 0
  name  = "${var.cluster_name}-cluster-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "eks.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags

  # Add lifecycle policy to handle existing resources
  lifecycle {
    create_before_destroy = true
  }
}

# Attach required IAM policies to the cluster role
resource "aws_iam_role_policy_attachment" "cluster_AmazonEKSClusterPolicy" {
  count      = var.create_iam_role ? 1 : 0
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"
  role       = aws_iam_role.cluster[0].name
}

resource "aws_iam_role_policy_attachment" "cluster_AmazonEKSVPCResourceController" {
  count      = var.create_iam_role ? 1 : 0
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSVPCResourceController"
  role       = aws_iam_role.cluster[0].name
}

# This security group rule is now defined in main.tf to avoid duplication

