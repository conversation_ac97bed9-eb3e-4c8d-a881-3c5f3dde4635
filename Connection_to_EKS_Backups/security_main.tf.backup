resource "aws_security_group" "bastion_sg" {
  name        = "${var.environment}-bastion-sg"
  description = "Security group for bastion host with restricted access"
  vpc_id      = var.vpc_id

  # Explicit deny for all inbound traffic
  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Explicit deny all inbound traffic"
  }

  # Allow SSH only from specific IP ranges
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.allowed_ssh_cidr_blocks
    description = "SSH access from specific IP ranges only"
  }

  # Explicit deny for all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Explicit deny all outbound traffic"
  }

  # Allow outbound traffic within VPC
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [var.vpc_cidr]
    description = "Allow all outbound traffic within VPC"
  }

  tags = merge(var.tags, {
    Name          = "${var.environment}-bastion-sg",
    Purpose       = "Bastion Host Access",
    SecurityLevel = "High"
  })
}

resource "aws_security_group" "rds_sg" {
  name        = "${var.environment}-rds-sg"
  description = "Security group for RDS instances with restricted access"
  vpc_id      = var.vpc_id

  # Explicit deny for all inbound traffic
  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Explicit deny all inbound traffic"
  }

  # Allow MySQL access only from within VPC
  ingress {
    description = "Allow MySQL access from within VPC"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  # Explicit deny for all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Explicit deny all outbound traffic"
  }

  # Allow outbound traffic within VPC
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [var.vpc_cidr]
    description = "Allow all outbound traffic within VPC"
  }

  tags = merge(var.tags, {
    Name          = "${var.environment}-rds-sg",
    Purpose       = "Database Access",
    SecurityLevel = "High"
  })
}

# Enable VPC Flow Logs for all security groups
resource "aws_flow_log" "security_groups" {
  iam_role_arn    = aws_iam_role.flow_log_role.arn
  log_destination = aws_cloudwatch_log_group.flow_logs.arn
  traffic_type    = "ALL"
  vpc_id          = var.vpc_id

  tags = merge(var.tags, {
    Name    = "${var.environment}-security-flow-logs",
    Purpose = "Security Monitoring"
  })
}

# IAM role for VPC Flow Logs
resource "aws_iam_role" "flow_log_role" {
  name = "${var.environment}-flow-log-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "vpc-flow-logs.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(var.tags, {
    Name    = "${var.environment}-flow-log-role",
    Purpose = "Flow Log Access"
  })
}

# CloudWatch Log Group for VPC Flow Logs
resource "aws_cloudwatch_log_group" "flow_logs" {
  name              = "/aws/vpc-flow-logs/${var.environment}"
  retention_in_days = 90

  tags = merge(var.tags, {
    Name    = "${var.environment}-flow-logs",
    Purpose = "Flow Log Storage"
  })
}
