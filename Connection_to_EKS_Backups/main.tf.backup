# Modified main.tf with updated module configurations

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

module "vpc" {
  source              = "./modules/vpc"
  region              = var.aws_region
  environment         = var.environment
  vpc_cidr            = var.vpc_cidr
  public_subnet_cidrs = var.public_subnets
  private_subnets     = var.private_subnets
  azs                 = ["${var.aws_region}a", "${var.aws_region}b", "${var.aws_region}c"]
  tags                = local.all_tags

  # Pass EKS cluster information - will be populated after EKS cluster creation
  cluster_name       = var.cluster_name
  cluster_endpoint   = try(module.eks.cluster_endpoint, "")
  cluster_ca_cert    = try(module.eks.cluster_ca_certificate, "")
  cluster_auth_token = try(module.eks.cluster_auth_token, "")

  # DNS settings are hardcoded in the VPC module
}

# IAM roles for EKS cluster are now created in the EKS module

# Create EKS cluster
module "eks" {
  source = "./modules/eks"

  vpc_cidr                  = var.vpc_cidr
  cluster_name              = var.cluster_name
  subnet_ids                = module.vpc.private_subnet_ids
  vpc_id                    = module.vpc.vpc_id
  environment               = var.environment
  kubernetes_version        = "1.32"
  tags                      = local.all_tags
  bastion_public_ip         = local.bastion_public_ip != "" ? local.bastion_public_ip : var.bastion_public_ip
  bastion_security_group_id = module.bastion.security_group_id

  # Security improvements
  kms_key_arn             = module.kms.primary_key_arn # Use KMS key for EKS encryption
  enable_network_policies = true                       # Enable network policies for pod-to-pod communication
  endpoint_public_access  = true                       # Enable public access to EKS API for bastion host
  endpoint_private_access = true                       # Enable private access to EKS API

  # Node configuration - increased size for more pods
  instance_types     = ["t3a.large"] # Use larger instance type for more pods
  node_instance_type = "t3a.large"   # Use larger instance type for more pods
  desired_size       = 2             # Start with 2 nodes
  max_size           = 4             # Allow up to 4 nodes
  min_size           = 1             # Minimum 1 node

  # Backend pod role configuration
  create_backend_pod_role = false # Disable backend pod role creation to avoid count dependency issues

  # Cost optimization
  enable_spot_instances = true        # Enable spot instances for non-critical workloads
  spot_instance_type    = "t3a.large" # Use t3a.large for spot instances
  spot_desired_size     = 1           # Start with 1 spot instance
  spot_max_size         = 3           # Allow up to 3 spot instances
  spot_min_size         = 0           # Allow scaling down to 0

  # Performance optimization
  enable_cluster_autoscaler = true # Enable cluster autoscaler for automatic scaling

  # Create new EKS cluster and resources
  create_eks                     = true # Create new EKS cluster
  create_iam_role                = true # Create new IAM roles
  create_security_groups         = true # Create new security groups
  create_node_groups             = true # Create new node groups
  create_iam_policies            = true # Create new IAM policies
  create_eks_log_group           = true # Create new CloudWatch log group
  eks_cluster_iam_role_arn       = local.eks_cluster_role_arn
  validate_network               = false                                                                         # Disable network validation for now
  validate_subnet_cidrs          = var.private_subnets                                                           # Use the predefined subnets
  create_endpoints               = false                                                                         # Control VPC endpoint creation
  enable_vpc_endpoint_validation = false                                                                         # Disable VPC endpoint validation
  skip_eks_connectivity_check    = true                                                                          # Skip EKS connectivity check
  route_table_ids                = concat(module.vpc.private_route_table_ids, module.vpc.public_route_table_ids) # Route tables for VPC endpoints
  create_s3_vpc_endpoint         = false                                                                         # Let the VPC module create the S3 endpoint
  check_if_cluster_exists        = false                                                                         # Don't check if cluster exists
  create_oidc_provider           = true                                                                          # Create new OIDC provider
  skip_kubernetes_resources      = false                                                                         # Deploy Kubernetes resources including cluster autoscaler

  # Connect to the bastion host
  bastion_role_arn = "arn:aws:iam::545009857703:role/production-eks-bastion-role"

  # Node group configuration - ensure nodes are created
  node_groups = {
    main = {
      name           = "production-wks-node-group"
      desired_size   = 2
      min_size       = 1
      max_size       = 3
      instance_types = ["t3a.medium"]
      disk_size      = 20
    }
  }

  # No explicit dependency needed - references to VPC outputs create implicit dependency
}


# Create a temporary IAM role for backend pods
# This role is used by the KMS module since we're not creating the backend pod role in the EKS module
resource "aws_iam_role" "temp_backend_pod_role" {
  name = "${var.environment}-temp-backend-pod-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "eks.amazonaws.com"
        }
      }
    ]
  })

  tags = local.all_tags
}

# Configure KMS module
module "kms" {
  source      = "./modules/kms"
  environment = var.environment
  tags        = local.all_tags

  # Use the temporary role since EKS module doesn't provide one yet
  backend_pod_role_arn = aws_iam_role.temp_backend_pod_role.arn

  tenants            = var.tenants != null ? var.tenants : {}
  administrator_arns = var.kms_administrator_arns != null ? var.kms_administrator_arns : []
  user_arns          = var.kms_user_arns != null ? var.kms_user_arns : []

  key_deletion_window_in_days = var.kms_key_deletion_window_in_days != null ? var.kms_key_deletion_window_in_days : 7
}

module "security" {
  source      = "./modules/security"
  environment = var.environment
  vpc_id      = module.vpc.vpc_id
  vpc_cidr    = var.vpc_cidr
  tags        = local.all_tags
}

module "rds" {
  source              = "./modules/rds"
  environment         = var.environment
  vpc_id              = module.vpc.vpc_id
  subnet_ids          = module.vpc.private_subnet_ids
  tenant_kms_key_arns = {} # Empty map since KMS keys have been disabled
  security_group_id   = module.security.rds_security_group_id
  tags = merge(
    {
      Environment = "production"
      ManagedBy   = "Terraform"
    },
    local.all_tags
  )
  kms_key_arn         = null # KMS keys removed to avoid deletion protection
  enable_proxy        = true # Enable RDS Proxy for connection pooling and additional security
  skip_final_snapshot = true # Skip final snapshot for easier development and testing

  # RDS configuration from terraform.tfvars
  instance_class          = var.rds_instance_class
  engine_version          = var.rds_engine_version
  backup_retention_period = var.db_backup_retention_period
  multi_az                = var.db_multi_az
  deletion_protection     = var.db_deletion_protection

  # Aurora Serverless v2 configuration
  use_aurora_serverless = true # Enable Aurora Serverless v2
  aurora_min_capacity   = 0.5  # Minimum capacity in ACUs
  aurora_max_capacity   = 16   # Maximum capacity in ACUs
  aurora_instance_count = 2    # 1 writer + 1 reader
  availability_zones    = ["eu-central-1a", "eu-central-1b", "eu-central-1c"]

  # Set import_existing_resources to false to create new resources
  import_existing_resources = false

  identifier = "production-architrave-db-new"

  db_name     = "architrave_db"
  db_username = "admin"                # You should use a more secure username in production
  db_password = "your-secure-password" # You should use a more secure password in production
}

# Fixed Route53 module configuration
module "route53" {
  source      = "./modules/route53"
  environment = var.environment
  domain_name = var.domain_name
  create_zone = true # Create a new zone
  vpc_id      = var.private_zone ? module.vpc.vpc_id : ""
  tags        = local.all_tags
}

# ACM Certificate module
module "acm" {
  source          = "./modules/acm"
  domain_name     = var.domain_name
  route53_zone_id = module.route53.zone_id
  environment     = var.environment
  tags            = local.all_tags
}

# Application Load Balancer module
module "alb" {
  source             = "./modules/alb"
  environment        = var.environment
  vpc_id             = module.vpc.vpc_id
  public_subnet_ids  = module.vpc.public_subnet_ids
  private_subnet_ids = module.vpc.private_subnet_ids
  vpc_subnet_ids     = module.vpc.public_subnet_ids # Use public subnets as fallback
  certificate_arn    = var.certificate_arn != "" ? var.certificate_arn : module.acm.certificate_arn
  domain_name        = var.domain_name
  route53_zone_id    = module.route53.zone_id
  tags               = local.all_tags
  alb_exists         = false # Set to false since we've deleted the ALB
  vpc_cidr           = var.vpc_cidr
  enable_access_logs = var.enable_access_logs # Pass the enable_access_logs variable to the ALB module

  # Security enhancements - restrict access to specific CIDR blocks
  # Use allowed_cidr_blocks if defined, otherwise use an empty list (which will default to VPC CIDR)
  allowed_http_cidr_blocks  = var.allowed_cidr_blocks
  allowed_https_cidr_blocks = var.allowed_cidr_blocks
}

# Specific module has been permanently removed
# Certain services are not permitted in this infrastructure

module "dynamodb" {
  source                = "./modules/dynamodb"
  environment           = var.environment
  eks_oidc_provider_url = try(module.eks.oidc_provider_url, "")
  namespace             = "default"
  serviceaccount        = "tenant-app-sa"
  kms_key_arn           = null # KMS keys removed to avoid deletion protection
  tags                  = local.all_tags
}

# Security tools module for Trivy, Falco, Fluentd, and Istio
# Security tools module for Trivy, Falco, Fluentd, and Istio
module "security_tools" {
  source         = "./modules/security_tools"
  cluster_name   = var.cluster_name
  environment    = var.environment
  enable_trivy   = true
  enable_falco   = true
  enable_fluentd = true
  enable_istio   = true # Enabled with optimized resources
  kms_key_arn    = module.kms.primary_key_arn

  # Configure ECR repositories for scanning
  # These repositories already exist, so we'll just update their scanning configuration
  ecr_repositories = {
    "nginx_dev"        = {},
    "webapp_dev"       = {},
    "rabbitmq_dev"     = {},
    "webapp_queue_dev" = {}
  }

  # Skip creating new repositories since they already exist
  skip_existing_resources = true

  # Add alert emails for notifications
  alert_emails = var.alert_emails

  tags = local.all_tags
}

# AWS Security Hub module
module "security_hub" {
  source                     = "./modules/security_hub"
  environment                = var.environment
  skip_security_hub_creation = true # Skip creation since it already exists
  alert_emails               = var.alert_emails
  tags                       = local.all_tags
  kms_key_arn                = module.kms.primary_key_arn
}

# AWS GuardDuty module
module "guardduty" {
  source                      = "./modules/guardduty"
  environment                 = var.environment
  alert_emails                = var.alert_emails
  tags                        = local.all_tags
  skip_publishing_destination = true # Skip creating the publishing destination to avoid permission issues
  kms_key_arn                 = module.kms.primary_key_arn
}

# AWS CloudTrail module
module "cloudtrail" {
  count              = var.enable_cloudtrail ? 1 : 0
  source             = "./modules/cloudtrail"
  environment        = var.environment
  alert_emails       = var.alert_emails
  log_retention_days = 90
  force_destroy      = true # Enable force destroy for easier cleanup during development
  tags               = local.all_tags
  kms_key_arn        = module.kms.primary_key_arn
}

# AWS Inspector module
module "inspector" {
  count        = var.enable_inspector ? 1 : 0
  source       = "./modules/inspector"
  environment  = var.environment
  alert_emails = var.alert_emails
  kms_key_arn  = module.kms.primary_key_arn
  tags         = local.all_tags
}

# AWS Config module
module "aws_config" {
  source                    = "./modules/aws_config"
  environment               = var.environment
  tags                      = local.all_tags
  skip_config_creation      = false # Always create AWS Config to comply with security best practices
  import_existing_resources = var.import_existing_resources
  kms_key_arn               = module.kms.primary_key_arn
}

# Bastion host module
module "bastion" {
  source      = "./modules/bastion"
  environment = var.environment

  # Required parameters
  subnet_id = module.vpc.public_subnet_ids[0] # Choose first public subnet
  vpc_id    = module.vpc.vpc_id
  vpc_cidr  = var.vpc_cidr
  region    = var.aws_region

  # EKS cluster information
  eks_cluster_name = var.cluster_name

  # Security settings - allow SSH access from anywhere for bastion host
  allowed_ssh_cidr_blocks = ["0.0.0.0/0"] # Allow SSH access from anywhere
  enable_ssh_access       = true

  # Set a monitoring CIDR - adjust as needed
  monitoring_bastion_cidr = var.vpc_cidr

  # Pass through the feature flag variables from root
  enable_vpc_endpoints         = false # Use existing VPC endpoints
  enable_cloudwatch_encryption = var.enable_cloudwatch_encryption
  enable_container_insights    = var.enable_container_insights
  skip_eks_connectivity_check  = true # Skip EKS connectivity check

  # Other settings
  create_ssm_endpoint   = false # Use existing SSM endpoint
  existing_ssm_endpoint = true

  # Use existing key pair
  ssh_key_name = "production-bastion-key"

  # Use existing resources
  create_ssh_key            = false
  import_existing_resources = true

  tags = local.all_tags
}

# Keep your existing locals block
locals {
  eks_ready                = fileexists("${path.module}/.eks_ready")
  skip_k8s                 = var.is_ci_cd ? false : true
  tenant_state             = "onboard"
  tenant_id                = var.customer_id
  fqdn                     = var.domain_name
  app_tenant               = "tenant-app"
  app_tenant_email         = "<EMAIL>"
  app_tenant_support_email = "<EMAIL>"
  app_sysadmin_name        = "admin"
  app_sysadmin_email       = "<EMAIL>"
  app_support_email        = "<EMAIL>"
  tenant_details_directory = "./tenant-details"
  image_tag                = "latest"
  # Removed duplicate bastion_public_ip local value - already defined in data.tf

  # Define module names being used for validation
  used_modules = ["vpc", "eks", "kms", "security", "rds", "route53", "dynamodb", "security_tools"]

  # vpc_settings is defined in vpc_config.tf

  mandatory_tags = {
    Environment        = var.environment
    Owner              = var.owner
    Project            = var.project
    CostCenter         = var.cost_center
    DataClassification = var.data_classification
    Compliance         = var.compliance_requirements
    ManagedBy          = "Terraform"
    CreationDate       = timestamp()
  }

  # Merge mandatory tags with any additional tags
  all_tags = merge(local.mandatory_tags, var.tags)
}

# Autoscaling module for HPA, VPA, and PDB - only include when not in CI/CD mode
module "autoscaling" {
  count                 = var.is_ci_cd ? 0 : 1
  source                = "./modules/autoscaling"
  cluster_name          = var.cluster_name
  environment           = var.environment
  eks_oidc_provider_url = module.eks.oidc_provider_url
  cluster_endpoint      = module.eks.cluster_endpoint

  # FIXED: Modern Karpenter configuration for 100-tenant scaling
  install_karpenter       = true
  karpenter_chart_version = "1.6.2"  # Updated to latest version
  instance_types          = ["t3.medium", "t3.large", "t3.xlarge", "m5.large", "m5.xlarge"]  # More variety for workloads
  max_cpu                 = "2000"    # Increased for 100 tenants
  max_memory              = "2000Gi"  # Increased for 100 tenants

  # VPA configuration
  install_vpa       = true
  vpa_chart_version = "1.4.0"

  # KEDA configuration for queue-based scaling - DISABLED, using keda module
  install_keda        = false
  keda_chart_version  = "2.17.2"  # Updated to latest version

  # Goldilocks configuration
  install_goldilocks       = true
  goldilocks_chart_version = "4.8.1"

  # PDB configuration
  create_default_pdbs   = true
  min_available_percent = 50
  tenant_namespaces     = ["tenant-system", "default"]

  # Add dependency on EKS module
  depends_on = [module.eks]
}

# Service Mesh Implementation
module "service_mesh" {
  count  = var.is_ci_cd ? 0 : 1
  source = "./modules/service_mesh"

  # Required arguments
  environment           = var.environment
  name                  = "istio"
  region                = var.region
  eks_cluster_id        = module.eks.cluster_id
  eks_cluster_endpoint  = module.eks.cluster_endpoint
  eks_oidc_provider_arn = module.eks.oidc_provider_arn

  namespace        = "istio-system"
  create_namespace = true

  # Skip Kubernetes connection due to connectivity issues
  skip_k8s_connection = true

  # Istio configuration
  istio_version = "1.16.1"

  # Ensure no deletion protection is enabled for testing

  # Resource configuration - highly optimized for pod limit constraints
  pilot_resources = {
    requests = {
      cpu    = "50m"
      memory = "64Mi"
    }
    limits = {
      cpu    = "200m"
      memory = "256Mi"
    }
  }

  proxy_resources = {
    requests = {
      cpu    = "5m"
      memory = "32Mi"
    }
    limits = {
      cpu    = "50m"
      memory = "64Mi"
    }
  }

  # Gateway configuration
  deploy_ingress_gateway       = true
  ingress_gateway_service_type = "LoadBalancer"
  ingress_gateway_internal     = true

  # Optimize ingress gateway resources
  ingress_gateway_resources = {
    requests = {
      cpu    = "50m"
      memory = "64Mi"
    }
    limits = {
      cpu    = "200m"
      memory = "256Mi"
    }
  }

  # Visualization and monitoring
  enable_kiali      = true
  kiali_version     = "1.57.1"
  kiali_hostname    = "kiali.${var.domain_name}"
  prometheus_url    = "http://prometheus-server.monitoring.svc.cluster.local:80"
  grafana_url       = "http://prometheus-grafana.monitoring.svc.cluster.local:80"
  enable_prometheus = true
  enable_grafana    = true

  # Tracing configuration
  enable_tracing  = true
  enable_jaeger   = true
  jaeger_version  = "0.47.0"
  jaeger_hostname = "jaeger.${var.domain_name}"

  # Application namespaces
  app_namespaces = [
    "default",
    "tenant-app"
  ]

  # Namespaces to inject Istio sidecar
  namespaces_to_inject = [
    "default",
    "tenant-app"
  ]

  # Security configuration
  enable_mtls = true
  mtls_mode   = "PERMISSIVE"

  # Gateway configuration
  create_gateway = true
  gateway_name   = "default-gateway"
  gateway_hosts  = ["*"]

  # Grafana dashboards
  create_grafana_dashboard = true
  grafana_namespace        = "monitoring"

  # Skip existing resources to avoid errors
  skip_existing_resources = true

  eks_cluster_certificate_authority_data = module.eks.cluster_ca_certificate
}

# KEDA (Kubernetes Event-Driven Autoscaling) module
module "keda" {
  count  = var.is_ci_cd ? 0 : 1
  source = "./modules/keda"

  environment         = var.environment
  namespace           = "keda"
  create_namespace    = true
  keda_install_source = var.keda_install_source

  # Enable Kubernetes connection
  skip_k8s_connection = var.skip_k8s_connection
}

# Tenant resource quotas
module "tenant_quotas" {
  count  = var.is_ci_cd ? 0 : 1
  source = "./modules/tenant-quotas"
  
  tenant_quotas = {
    "tenant-aug-272" = {
      requests_cpu    = "5000m"
      requests_memory = "10Gi"
      limits_cpu      = "8000m"
      limits_memory   = "16Gi"
      pods            = "200"
    }
    "tenant-aug-453" = {
      requests_cpu    = "3000m"
      requests_memory = "6Gi"
      limits_cpu      = "5000m"
      limits_memory   = "10Gi"
      pods            = "150"
    }
    "tenant-aug-datatest" = {
      requests_cpu    = "2000m"
      requests_memory = "4Gi"
      limits_cpu      = "4000m"
      limits_memory   = "8Gi"
      pods            = "100"
    }
  }
}

# Tenant Management
module "tenant_management" {
  source                     = "./modules/tenant_management"
  environment                = var.environment
  name                       = var.name
  tags                       = var.tags
  region                     = var.region
  eks_oidc_provider_arn      = module.eks.oidc_provider_arn
  eks_oidc_provider_url      = module.eks.oidc_provider_url
  db_name                    = var.db_name
  db_host                    = var.db_host
  db_username                = var.db_username
  db_password                = var.db_password
  eks_cluster_endpoint       = module.eks.cluster_endpoint
  eks_cluster_ca_certificate = module.eks.cluster_ca_certificate
  eks_cluster_name           = module.eks.cluster_name
  tenants                    = var.tenants
  skip_tenant_resources      = var.skip_tenant_resources
  skip_k8s_connection        = var.skip_k8s_connection
  skip_kubernetes_resources  = var.skip_kubernetes_resources
  account_id                 = var.account_id != null ? var.account_id : local.aws_account_id
}

# Outputs
output "waf_web_acl_arns" {
  description = "ARNs of the WAF Web ACLs created for each tenant"
  value       = module.tenant_management.waf_web_acl_arns
}

output "monitoring_dashboard_urls" {
  description = "URLs of the CloudWatch dashboards created for each tenant"
  value       = module.tenant_management.monitoring_dashboard_urls
}

# Comment out outputs that are not yet implemented
# output "backup_vault_arns" {
#   description = "ARNs of the backup vaults created for each tenant"
#   value       = module.tenant_management.backup_vault_arns
# }

# output "compliance_rule_arns" {
#   description = "ARNs of the compliance rules created for each tenant"
#   value       = module.tenant_management.compliance_rule_arns
# }

# output "dr_test_function_arns" {
#   description = "ARNs of the DR test Lambda functions created for each tenant"
#   value       = module.tenant_management.dr_test_function_arns
# }

# output "security_alert_topic_arns" {
#   description = "ARNs of the security alert SNS topics created for each tenant"
#   value       = module.tenant_management.security_alert_topic_arns
# }

# output "infra_monitoring_dashboard_urls" {
#   description = "URLs of the infrastructure monitoring dashboards created for each tenant"
#   value       = module.tenant_management.infra_monitoring_dashboard_urls
# }

module "tenant_onboarding" {
  source                         = "./modules/tenant_onboarding"
  environment                    = var.environment
  name                           = var.name
  tags                           = var.tags
  operator_namespace             = var.operator_namespace
  create_operator_namespace      = var.create_operator_namespace
  operator_image_repository      = var.operator_image_repository
  operator_image_tag             = var.operator_image_tag
  operator_replicas              = var.operator_replicas
  operator_log_level             = var.operator_log_level
  operator_resources             = var.operator_resources
  operator_role_arn              = var.operator_role_arn
  eks_oidc_provider_arn          = module.eks.oidc_provider_arn
  eks_oidc_provider_url          = module.eks.oidc_provider_url
  eks_oidc_provider              = "" # Set if needed
  region                         = var.region
  tenant_kms_key_arns            = var.tenant_kms_key_arns
  database_secret_arn            = var.database_secret_arn
  s3_bucket_name                 = var.s3_bucket_name
  default_resource_quota_cpu     = var.default_resource_quota_cpu
  default_resource_quota_memory  = var.default_resource_quota_memory
  default_resource_quota_storage = var.default_resource_quota_storage
  default_resource_quota_pods    = var.default_resource_quota_pods
  enable_network_policies        = var.enable_network_policies
  enable_pod_security_policies   = var.enable_pod_security_policies
  enable_istio_mtls              = var.enable_istio_mtls
  enable_monitoring              = var.enable_monitoring
  prometheus_namespace           = var.prometheus_namespace
  grafana_namespace              = var.grafana_namespace
  skip_k8s_connection            = var.skip_k8s_connection
  skip_kubernetes_resources      = var.skip_kubernetes_resources
  skip_tenant_resources          = var.skip_tenant_resources
  tenants                        = var.tenants
  db_name                        = var.db_name
  db_host                        = var.db_host
  db_username                    = var.db_username
  db_password                    = var.db_password
  eks_cluster_endpoint           = module.eks.cluster_endpoint
  eks_cluster_ca_certificate     = module.eks.cluster_ca_certificate
  eks_cluster_name               = module.eks.cluster_name
  account_id                     = data.aws_caller_identity.current.account_id
}

module "tenant_dashboard_provisioner" {
  source                         = "./modules/tenant_dashboard_provisioner"
  environment                    = var.environment
  name                           = var.name
  tags                           = var.tags
  operator_namespace             = var.operator_namespace
  create_operator_namespace      = var.create_operator_namespace
  operator_image_repository      = var.operator_image_repository
  operator_image_tag             = var.operator_image_tag
  operator_replicas              = var.operator_replicas
  operator_log_level             = var.operator_log_level
  operator_resources             = var.operator_resources
  operator_role_arn              = var.operator_role_arn
  eks_oidc_provider_arn          = module.eks.oidc_provider_arn
  eks_oidc_provider_url          = module.eks.oidc_provider_url
  eks_oidc_provider              = "" # Set if needed
  region                         = var.region
  tenant_kms_key_arns            = var.tenant_kms_key_arns
  database_secret_arn            = var.database_secret_arn
  s3_bucket_name                 = var.s3_bucket_name
  account_id                     = data.aws_caller_identity.current.account_id
  default_resource_quota_cpu     = var.default_resource_quota_cpu
  default_resource_quota_memory  = var.default_resource_quota_memory
  default_resource_quota_storage = var.default_resource_quota_storage
  default_resource_quota_pods    = var.default_resource_quota_pods
  enable_network_policies        = var.enable_network_policies
  enable_pod_security_policies   = var.enable_pod_security_policies
  enable_istio_mtls              = var.enable_istio_mtls
  enable_monitoring              = var.enable_monitoring
  prometheus_namespace           = var.prometheus_namespace
  grafana_namespace              = var.grafana_namespace
  skip_k8s_connection            = var.skip_k8s_connection
  skip_kubernetes_resources      = var.skip_kubernetes_resources
  skip_tenant_resources          = var.skip_tenant_resources
  tenants                        = var.tenants
  db_name                        = var.db_name
  db_host                        = var.db_host
  db_username                    = var.db_username
  db_password                    = var.db_password
  eks_cluster_endpoint           = module.eks.cluster_endpoint
  eks_cluster_ca_certificate     = module.eks.cluster_ca_certificate
  eks_cluster_name               = module.eks.cluster_name
}

module "tenant_operator" {
  source                         = "./modules/tenant_operator"
  environment                    = var.environment
  name                           = var.name
  tags                           = var.tags
  operator_namespace             = var.operator_namespace
  create_operator_namespace      = var.create_operator_namespace
  operator_image_repository      = var.operator_image_repository
  operator_image_tag             = var.operator_image_tag
  operator_replicas              = var.operator_replicas
  operator_log_level             = var.operator_log_level
  operator_resources             = var.operator_resources
  operator_role_arn              = var.operator_role_arn
  eks_oidc_provider_arn          = module.eks.oidc_provider_arn
  eks_oidc_provider_url          = module.eks.oidc_provider_url
  eks_oidc_provider              = "" # Set if needed
  region                         = var.region
  tenant_kms_key_arns            = var.tenant_kms_key_arns
  database_secret_arn            = var.database_secret_arn
  s3_bucket_name                 = var.s3_bucket_name
  account_id                     = var.account_id != null ? var.account_id : local.aws_account_id
  default_resource_quota_cpu     = var.default_resource_quota_cpu
  default_resource_quota_memory  = var.default_resource_quota_memory
  default_resource_quota_storage = var.default_resource_quota_storage
  default_resource_quota_pods    = var.default_resource_quota_pods
  enable_network_policies        = var.enable_network_policies
  enable_pod_security_policies   = var.enable_pod_security_policies
  enable_istio_mtls              = var.enable_istio_mtls
  enable_monitoring              = var.enable_monitoring
  prometheus_namespace           = var.prometheus_namespace
  grafana_namespace              = var.grafana_namespace
  skip_k8s_connection            = var.skip_k8s_connection
  skip_kubernetes_resources      = var.skip_kubernetes_resources
  skip_tenant_resources          = var.skip_tenant_resources
  tenants                        = var.tenants
  db_name                        = var.db_name
  db_host                        = var.db_host
  db_username                    = var.db_username
  db_password                    = var.db_password
  eks_cluster_endpoint           = module.eks.cluster_endpoint
  eks_cluster_ca_certificate     = module.eks.cluster_ca_certificate
  eks_cluster_name               = module.eks.cluster_name
}

module "tenant" {
  source = "./modules/tenant"

  environment = var.environment
  name        = var.name
  tags        = var.tags

  eks_oidc_provider_arn      = module.eks.oidc_provider_arn
  eks_oidc_provider_url      = module.eks.oidc_provider_url
  region                     = var.region
  tenant_kms_key_arns        = var.tenant_kms_key_arns
  database_secret_arn        = var.database_secret_arn
  s3_bucket_name             = var.s3_bucket_name
  tenants                    = var.tenants
  operator_namespace         = var.operator_namespace
  operator_role_arn          = var.operator_role_arn
  skip_k8s_connection        = var.skip_k8s_connection
  skip_kubernetes_resources  = var.skip_kubernetes_resources
  skip_tenant_resources      = var.skip_tenant_resources
  account_id                 = data.aws_caller_identity.current.account_id
  db_name                    = var.db_name
  db_host                    = var.db_host
  db_username                = var.db_username
  db_password                = var.db_password
  eks_cluster_endpoint       = module.eks.cluster_endpoint
  eks_cluster_ca_certificate = module.eks.cluster_ca_certificate
  eks_cluster_name           = module.eks.cluster_name
}

module "tenant_isolation" {
  source = "./modules/tenant_isolation"

  environment = var.environment
  name        = var.name
  tags        = var.tags

  eks_oidc_provider_arn      = module.eks.oidc_provider_arn
  eks_oidc_provider_url      = module.eks.oidc_provider_url
  region                     = var.region
  tenant_kms_key_arns        = var.tenant_kms_key_arns
  database_secret_arn        = var.database_secret_arn
  s3_bucket_name             = var.s3_bucket_name
  tenants                    = var.tenants
  operator_namespace         = var.operator_namespace
  operator_role_arn          = var.operator_role_arn
  skip_k8s_connection        = var.skip_k8s_connection
  skip_kubernetes_resources  = var.skip_kubernetes_resources
  skip_tenant_resources      = var.skip_tenant_resources
  account_id                 = data.aws_caller_identity.current.account_id
  db_name                    = var.db_name
  db_host                    = var.db_host
  db_username                = var.db_username
  db_password                = var.db_password
  eks_cluster_endpoint       = module.eks.cluster_endpoint
  eks_cluster_ca_certificate = module.eks.cluster_ca_certificate
  eks_cluster_name           = module.eks.cluster_name
}

# Istio Service Mesh Module
module "istio" {
  source = "./modules/istio"

  environment         = var.environment
  name               = var.name
  tags               = var.tags
  skip_k8s_connection = var.skip_k8s_connection
  skip_kubernetes_resources = var.skip_kubernetes_resources
  domain_name        = var.domain_name

  # Istio configuration
  istio_version          = var.istio_version
  mesh_id               = var.mesh_id
  cluster_network       = var.cluster_network
  trace_sampling        = var.trace_sampling
  enable_ingress_gateway = var.enable_istio_ingress_gateway
  enable_egress_gateway  = var.enable_istio_egress_gateway
  enable_mtls           = var.enable_istio_mtls
  mtls_mode             = var.istio_mtls_mode
  pilot_resources       = var.istio_pilot_resources
  circuit_breaker       = var.istio_circuit_breaker
  tenants               = var.tenants

  depends_on = [module.eks]
}

# API Management Module
module "api_management" {
  source = "./modules/api_management"

  environment         = var.environment
  name               = var.name
  tags               = var.tags
  api_gateway_type   = var.api_gateway_type
  domain_name        = var.domain_name
  certificate_arn    = data.aws_acm_certificate.domain_certificate[0].arn
  vpc_id             = module.vpc.vpc_id
  private_subnet_ids = module.vpc.private_subnet_ids
  alb_arn           = module.alb.alb_arn
  istio_namespace   = module.istio.istio_namespace
  skip_k8s_connection = var.skip_k8s_connection

  # API configuration
  tenants              = var.tenants
  rate_limiting        = var.api_rate_limiting
  api_versions         = var.api_versions
  latency_threshold    = var.api_latency_threshold
  error_rate_threshold = var.api_error_rate_threshold

  depends_on = [module.istio, module.alb]
}
