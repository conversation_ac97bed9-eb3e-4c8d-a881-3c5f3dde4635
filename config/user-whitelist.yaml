office_vpn:
  enabled: true
  ranges:
    - *************/29
departments:
  devops:
    enabled: true
    description: DevOps and Infrastructure team
    users:
      - name: <PERSON><PERSON>
        username: santosh.baruah
        ip: *************
        role: admin
        active: true
        added_date: "2025-01-14"
        notes: DevOps Lead - Temp enabled for VPN testing
      - name: Additional User
        username: additional.user
        ip: **************
        role: admin
        active: true
        added_date: "2025-01-14"
        notes: Additional authorized IP for EKS access
  engineering:
    enabled: true
    description: Software Engineering team
    users:
      - name: <PERSON>
        username: john.doe
        ip: *************
        role: developer
        active: false
        added_date: "2025-09-01"
        notes: Senior Developer
        deactivated_date: "2025-09-01"
  chd:
    enabled: true
    description: CHD team
    users:
      - name: <PERSON>
        username: jane.smith
        ip: ************
        role: viewer
        active: false
        added_date: "2025-09-01"
        notes: CHD Analyst
        deactivated_date: "2025-09-01"
  qa:
    enabled: false
    description: Quality Assurance team
    users: null
  support:
    enabled: false
    description: Technical Support team
    users: null
  management:
    enabled: false
    description: Management team
    users: null
