# Default values for architrave-tenant
# This is a YAML-formatted file.

# Tenant configuration
tenant:
  id: "example-tenant"
  domain: "example.architrave.com"
  name: ""  # Will be set during onboarding
  
# Container images - Updated to requested versions
images:
  webapp: "545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v25"
  nginx: "545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.8-preview-2"
  rabbitmq: "545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02"
  pullPolicy: IfNotPresent

# Database configuration
database:
  host: "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
  port: "3306"
  name: "tenant_test_data"  # Will be overridden during deployment
  username: "admin"

# Resource limits and requests
resources:
  webapp:
    requests:
      cpu: "50m"
      memory: "128Mi"
    limits:
      cpu: "200m"
      memory: "256Mi"
  nginx:
    requests:
      cpu: "25m"
      memory: "64Mi"
    limits:
      cpu: "100m"
      memory: "128Mi"
  queues:
    requests:
      cpu: "25m"
      memory: "64Mi"
    limits:
      cpu: "100m"
      memory: "128Mi"
  rabbitmq:
    requests:
      cpu: "50m"
      memory: "128Mi"
    limits:
      cpu: "200m"
      memory: "256Mi"

# Storage configuration
storage:
  s3:
    enabled: true
    bucketName: ""  # Will be set dynamically: tenant-{id}-storage
    region: "eu-central-1"
    size: "10Gi"
    storageClass: "s3-sc"
    mountOptions:
      - "uid=33"
      - "gid=33"
      - "allow-other"
      - "file-mode=0777"
      - "dir-mode=0777"

# CronJob configurations
cronjobs:
  processDocuments:
    enabled: false
    schedule: "*/5 * * * *"
    concurrencyPolicy: "Forbid"
    command: ["architrave-entrypoint-cron.sh"]
    args: ["bin/architrave cron:system:process-new-documents --limit=30"]
    
  processStaged:
    enabled: false
    schedule: "*/15 * * * *"
    concurrencyPolicy: "Forbid"
    command: ["architrave-entrypoint-cron.sh"]
    args: ["bin/architrave arch:delphi:process-new-staged-documents"]
    
  reprocess:
    enabled: false
    schedule: "*/10 * * * *"
    concurrencyPolicy: "Forbid"
    command: ["architrave-entrypoint-cron.sh"]
    args: ["bin/architrave cron:system:process-new-documents --repreview-errored-previews-only --limit=30"]

# Queue worker configurations
queues:
  default:
    enabled: true
    replicas: 1
    command: ["/bin/sh"]
    args: ["-c", "touch /tmp/queue_running && exec architrave-entrypoint-queue.sh default_worker_queue"]
    
  folder:
    enabled: true
    replicas: 1
    command: ["/bin/sh"]
    args: ["-c", "touch /tmp/queue_running && exec architrave-entrypoint-queue.sh folder_worker_queue"]
    
  notification:
    enabled: true
    replicas: 1
    command: ["/bin/sh"]
    args: ["-c", "touch /tmp/queue_running && exec architrave-entrypoint-queue.sh notification_worker_queue"]

# RabbitMQ configuration
rabbitmq:
  enabled: true
  replicas: 1
  config:
    defaultUser: "guest"
    defaultPass: "guest"
  management:
    enabled: true
  ports:
    amqp: 5672
    epmd: 4369
    management: 15672

# Webapp configuration
webapp:
  enabled: true
  replicas: 1
  service:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  livenessProbe:
    httpGet:
      path: /api/health
      port: 8080
    initialDelaySeconds: 30
    periodSeconds: 10
  readinessProbe:
    httpGet:
      path: /api/health
      port: 8080
    initialDelaySeconds: 5
    periodSeconds: 5

# Frontend configuration
frontend:
  enabled: true
  replicas: 1
  service:
    type: ClusterIP
    port: 80
    targetPort: 8080
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
  livenessProbe:
    httpGet:
      path: /health
      port: 8080
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: /health
      port: 8080
    initialDelaySeconds: 5
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 3

# Nginx configuration
nginx:
  enabled: false
  replicas: 1
  service:
    type: ClusterIP
    httpPort: 80
    httpsPort: 443
  livenessProbe:
    httpGet:
      path: /nginx-health
      port: 80
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: /nginx-health
      port: 80
    initialDelaySeconds: 15
    periodSeconds: 5
    timeoutSeconds: 5
    failureThreshold: 3

# Service configuration
service:
  type: ClusterIP
  
# Istio configuration
istio:
  enabled: true
  gateway:
    enabled: true
    hosts:
      - "{{ .Values.tenant.domain }}"
  virtualService:
    enabled: true
    
# Security configuration
security:
  podSecurityContext:
    runAsUser: 33
    runAsGroup: 33
    fsGroup: 33
  securityContext:
    allowPrivilegeEscalation: false
    runAsNonRoot: true
    runAsUser: 33
    capabilities:
      drop:
        - ALL

# Monitoring and health checks
monitoring:
  enabled: true
  healthCheck:
    enabled: true
    path: "/health"
    initialDelaySeconds: 30
    periodSeconds: 10
    
# Autoscaling (optional)
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# KEDA Configuration for Event-Driven Autoscaling
keda:
  enabled: true  # Enable KEDA scaling for all tenants
  
  # Queue-based scaling configuration
  queues:
    default:
      minReplicas: 1
      maxReplicas: 20      # Scale up to 20 for heavy onboarding
      queueThreshold: 5    # Scale when 5+ messages in queue
      pollingInterval: 15  # Check every 15 seconds
      cooldownPeriod: 300  # Wait 5 minutes before scaling down
      
    folder:
      minReplicas: 1
      maxReplicas: 15      # Folder processing can be intensive
      queueThreshold: 3    # Scale when 3+ messages in queue
      pollingInterval: 15
      cooldownPeriod: 300
      
    notification:
      minReplicas: 1
      maxReplicas: 8       # Notifications are lighter workload
      queueThreshold: 2    # Scale when 2+ messages in queue
      pollingInterval: 10  # Check more frequently for notifications
      cooldownPeriod: 180  # Scale down faster for notifications
  
  # WebApp scaling based on CPU/Memory
  webapp:
    minReplicas: 1
    maxReplicas: 10       # Scale webapp based on traffic
    cpuThreshold: 70      # Scale when CPU > 70%
    memoryThreshold: 80   # Scale when memory > 80%
    pollingInterval: 30   # Check every 30 seconds
    cooldownPeriod: 300   # Wait 5 minutes before scaling down

# Node selection
nodeSelector: {}
tolerations: []
affinity: {}

# Additional labels and annotations
labels: {}
annotations: {}

# Environment-specific overrides
environment: "production"

# Secrets configuration (will be overridden by onboarding script)
secrets:
  # AV Automate S3 credentials
  avAutomateS3Key: "5aef88a01e400845a837"
  avAutomateS3SecretKey: "sVOOSYFZai9a0OWzpOQxSY0x48WvmS6ve6C0roYY"

  # DCM keys
  dcmPrivateKey: "0088247850db89e38c2327c1aacdd263a74e2b271a35d2c39e1b9f50e306cebb"
  dcmPublicKey: "46fe06e9781a6a61f53cb38dfd9f33ef422f088c3bfcc050ebe0ecede7e6e95b"

  # Instance pre-shared key
  instancePreSharedKey: "123"

  # Database password (placeholder - will be updated during onboarding)
  mysqlPassword: "PLACEHOLDER_PASSWORD"

  # RabbitMQ password
  rabbitmqPassword: "guest"

  # Release notes API token
  releaseNotesApiToken: "asdasd"

# Cognito configuration
cognito:
  userPoolId: ""  # Will be set during onboarding
  clientId: ""    # Will be set during onboarding
  clientSecret: "" # Will be set during onboarding
  region: "eu-central-1"
  domain: ""      # Will be set during onboarding
