{{- if .Values.keda.enabled }}
# KEDA ScaledObject for Default Queue - Optimized for 100-tenant scaling
{{- if .Values.queues.default.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Values.tenant.id }}-default-queue-scaler
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: default-queue
    component: keda-scaler
    tenant-id: {{ .Values.tenant.id }}
    workload-type: tenant-queue
spec:
  scaleTargetRef:
    name: {{ .Values.tenant.id }}-default-queue
  minReplicaCount: {{ .Values.keda.queues.default.minReplicas | default 0 }}  # Scale to zero for cost optimization
  maxReplicaCount: {{ .Values.keda.queues.default.maxReplicas | default 20 }} # Increased for burst capacity
  pollingInterval: {{ .Values.keda.queues.default.pollingInterval | default 10 }} # Faster polling
  cooldownPeriod: {{ .Values.keda.queues.default.cooldownPeriod | default 120 }}  # Faster scale-down
  triggers:
    - type: rabbitmq
      metadata:
        protocol: amqp
        host: {{ .Values.tenant.id }}-rabbitmq:5672
        queueName: {{ .Values.tenant.id }}_default_queue
        queueLength: '{{ .Values.keda.queues.default.queueThreshold | default 3 }}' # More aggressive scaling
        vhostName: /
        username: {{ .Values.rabbitmq.config.defaultUser | default "guest" }}
      authenticationRef:
        name: {{ .Values.tenant.id }}-rabbitmq-auth
  behavior:
    scaleDown:
      stabilizationWindowSeconds: {{ .Values.keda.queues.default.scaleDownStabilization | default 60 }}
      policies:
      - type: Percent
        value: 50
        periodSeconds: 30
    scaleUp:
      stabilizationWindowSeconds: {{ .Values.keda.queues.default.scaleUpStabilization | default 0 }}
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 5  # Add up to 5 pods at once during bursts
        periodSeconds: 15
---
{{- end }}

# KEDA ScaledObject for Folder Queue
{{- if .Values.queues.folder.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Values.tenant.id }}-folder-queue-scaler
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: folder-queue
    component: keda-scaler
spec:
  scaleTargetRef:
    name: {{ .Values.tenant.id }}-folder-queue
  minReplicaCount: {{ .Values.keda.queues.folder.minReplicas | default 1 }}
  maxReplicaCount: {{ .Values.keda.queues.folder.maxReplicas | default 8 }}
  pollingInterval: {{ .Values.keda.queues.folder.pollingInterval | default 15 }}
  cooldownPeriod: {{ .Values.keda.queues.folder.cooldownPeriod | default 300 }}
  triggers:
    - type: rabbitmq
      metadata:
        protocol: amqp
        host: {{ .Values.tenant.id }}-rabbitmq:5672
        queueName: {{ .Values.tenant.id }}_folder_queue
        queueLength: '{{ .Values.keda.queues.folder.queueThreshold | default 3 }}'
        vhostName: /
        username: {{ .Values.rabbitmq.config.defaultUser | default "guest" }}
      authenticationRef:
        name: {{ .Values.tenant.id }}-rabbitmq-auth
---
{{- end }}

# KEDA ScaledObject for Notification Queue
{{- if .Values.queues.notification.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Values.tenant.id }}-notification-queue-scaler
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: notification-queue
    component: keda-scaler
spec:
  scaleTargetRef:
    name: {{ .Values.tenant.id }}-notification-queue
  minReplicaCount: {{ .Values.keda.queues.notification.minReplicas | default 1 }}
  maxReplicaCount: {{ .Values.keda.queues.notification.maxReplicas | default 5 }}
  pollingInterval: {{ .Values.keda.queues.notification.pollingInterval | default 10 }}
  cooldownPeriod: {{ .Values.keda.queues.notification.cooldownPeriod | default 180 }}
  triggers:
    - type: rabbitmq
      metadata:
        protocol: amqp
        host: {{ .Values.tenant.id }}-rabbitmq:5672
        queueName: {{ .Values.tenant.id }}_notification_queue
        queueLength: '{{ .Values.keda.queues.notification.queueThreshold | default 2 }}'
        vhostName: /
        username: {{ .Values.rabbitmq.config.defaultUser | default "guest" }}
      authenticationRef:
        name: {{ .Values.tenant.id }}-rabbitmq-auth
---
{{- end }}

# KEDA ScaledObject for WebApp (CPU/Memory based)
{{- if .Values.webapp.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Values.tenant.id }}-webapp-scaler
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: webapp
    component: keda-scaler
spec:
  scaleTargetRef:
    name: {{ .Values.tenant.id }}-webapp
  minReplicaCount: {{ .Values.keda.webapp.minReplicas | default 1 }}
  maxReplicaCount: {{ .Values.keda.webapp.maxReplicas | default 5 }}
  pollingInterval: {{ .Values.keda.webapp.pollingInterval | default 30 }}
  cooldownPeriod: {{ .Values.keda.webapp.cooldownPeriod | default 300 }}
  triggers:
    - type: cpu
      metadata:
        type: Utilization
        value: '{{ .Values.keda.webapp.cpuThreshold | default 70 }}'
    - type: memory
      metadata:
        type: Utilization
        value: '{{ .Values.keda.webapp.memoryThreshold | default 80 }}'
---
{{- end }}

# RabbitMQ Authentication Secret for KEDA
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.tenant.id }}-rabbitmq-auth
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    component: keda-auth
type: Opaque
data:
  password: {{ .Values.rabbitmq.config.defaultPass | default "guest" | b64enc }}
---

# KEDA ScaledObject for Onboarding Burst Processing
{{- if .Values.keda.onboarding.enabled | default true }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Values.tenant.id }}-onboarding-burst-scaler
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: onboarding-processor
    component: keda-scaler
    tenant-id: {{ .Values.tenant.id }}
    workload-type: onboarding-burst
    priority: critical
spec:
  scaleTargetRef:
    name: {{ .Values.tenant.id }}-onboarding-processor
  minReplicaCount: {{ .Values.keda.onboarding.minReplicas | default 0 }}
  maxReplicaCount: {{ .Values.keda.onboarding.maxReplicas | default 10 }}
  pollingInterval: {{ .Values.keda.onboarding.pollingInterval | default 5 }}  # Very fast polling
  cooldownPeriod: {{ .Values.keda.onboarding.cooldownPeriod | default 60 }}   # Quick scale-down
  triggers:
    - type: rabbitmq
      metadata:
        protocol: amqp
        host: {{ .Values.tenant.id }}-rabbitmq:5672
        queueName: {{ .Values.tenant.id }}_onboarding_queue
        queueLength: '{{ .Values.keda.onboarding.queueThreshold | default 1 }}' # 1 pod per message for fast processing
        vhostName: /
        username: {{ .Values.rabbitmq.config.defaultUser | default "guest" }}
      authenticationRef:
        name: {{ .Values.tenant.id }}-rabbitmq-auth
    # Additional trigger for database setup tasks
    - type: mysql
      metadata:
        queryValue: "SELECT COUNT(*) FROM tenant_setup_tasks WHERE tenant_id='{{ .Values.tenant.id }}' AND status='pending'"
        targetQueryValue: "1"
        connectionStringFromEnv: MYSQL_CONN_STRING
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 30  # Very fast scale-down for cost optimization
      policies:
      - type: Percent
        value: 100  # Scale down aggressively
        periodSeconds: 15
    scaleUp:
      stabilizationWindowSeconds: 0   # Immediate scale-up
      policies:
      - type: Percent
        value: 200  # Double capacity quickly
        periodSeconds: 10
      - type: Pods
        value: 5    # Add 5 pods at once for burst capacity
        periodSeconds: 10
---
{{- end }}

# KEDA ScaledObject for Multi-Tenant Database Connection Pool
{{- if .Values.keda.database.enabled | default true }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Values.tenant.id }}-db-pool-scaler
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: database-pool
    component: keda-scaler
    tenant-id: {{ .Values.tenant.id }}
    workload-type: database-connections
spec:
  scaleTargetRef:
    name: {{ .Values.tenant.id }}-db-connection-pool
  minReplicaCount: {{ .Values.keda.database.minReplicas | default 1 }}
  maxReplicaCount: {{ .Values.keda.database.maxReplicas | default 5 }}
  pollingInterval: {{ .Values.keda.database.pollingInterval | default 15 }}
  cooldownPeriod: {{ .Values.keda.database.cooldownPeriod | default 180 }}
  triggers:
    - type: mysql
      metadata:
        queryValue: "SELECT COUNT(*) FROM information_schema.processlist WHERE db='{{ .Values.tenant.id }}_db' AND command != 'Sleep'"
        targetQueryValue: "{{ .Values.keda.database.connectionThreshold | default 10 }}"
        connectionStringFromEnv: MYSQL_CONN_STRING
    - type: prometheus
      metadata:
        serverAddress: http://prometheus.monitoring.svc.cluster.local:9090
        metricName: mysql_tenant_connections
        threshold: '{{ .Values.keda.database.prometheusThreshold | default 15 }}'
        query: mysql_global_status_threads_connected{tenant="{{ .Values.tenant.id }}"}
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # Slow scale-down for DB connections
      policies:
      - type: Pods
        value: 1
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Pods
        value: 2
        periodSeconds: 30
---
{{- end }}

# KEDA TriggerAuthentication for RabbitMQ
apiVersion: keda.sh/v1alpha1
kind: TriggerAuthentication
metadata:
  name: {{ .Values.tenant.id }}-rabbitmq-auth
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    component: keda-auth
spec:
  secretTargetRef:
    - parameter: password
      name: {{ .Values.tenant.id }}-rabbitmq-auth
      key: password
{{- end }}