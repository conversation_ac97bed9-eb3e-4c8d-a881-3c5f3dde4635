{{- if .Values.istio.enabled }}
{{- if .Values.istio.virtualService.enabled }}
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: {{ .Values.tenant.id }}-virtualservice
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  hosts:
    - {{ .Values.tenant.domain }}
  gateways:
    - istio-system/tenant-gateway
  http:
    - match:
        - uri:
            prefix: /api/
      route:
        - destination:
            host: {{ .Values.tenant.id }}-webapp
            port:
              number: {{ .Values.webapp.service.port }}
      headers:
        request:
          set:
            X-Tenant-ID: {{ .Values.tenant.id }}
            X-Forwarded-Proto: "https"
      timeout: 30s
      retries:
        attempts: 3
        perTryTimeout: 10s
        retryOn: 5xx,reset,connect-failure,refused-stream
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: {{ .Values.tenant.id }}-webapp
            port:
              number: {{ .Values.webapp.service.port }}
      headers:
        request:
          set:
            X-Tenant-ID: {{ .Values.tenant.id }}
            X-Forwarded-Proto: "https"
      timeout: 30s
      retries:
        attempts: 3
        perTryTimeout: 10s
        retryOn: 5xx,reset,connect-failure,refused-stream
{{- end }}
{{- end }}
