{{- if .Values.nginx.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.tenant.id }}-nginx
  namespace: {{ .Values.tenant.namespace }}
  labels:
    app: {{ .Values.tenant.id }}-nginx
    app.kubernetes.io/name: {{ include "architrave-tenant.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    component: nginx
  annotations:
    owner: architrave
spec:
  replicas: {{ .Values.nginx.replicas | default 1 }}
  selector:
    matchLabels:
      app: {{ .Values.tenant.id }}-nginx
      app.kubernetes.io/name: {{ include "architrave-tenant.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      component: nginx
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: {{ .Values.tenant.id }}-nginx
        app.kubernetes.io/name: {{ include "architrave-tenant.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        component: nginx
      annotations:
        owner: architrave
    spec:
      securityContext:
        fsGroup: 33
      initContainers:
        - name: webapp-files-copier
          image: {{ .Values.images.webapp }}
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Copying webapp files to shared volume..."
              mkdir -p /tmp/webapp-files
              cp -r /storage/ArchAssets/public/* /tmp/webapp-files/ 2>/dev/null || true
              cp -r /storage/ArchAssets/public/. /tmp/webapp-files/ 2>/dev/null || true
              echo "Webapp files copied successfully"
              ls -la /tmp/webapp-files/
          volumeMounts:
            - mountPath: /tmp/webapp-files
              name: webapp-files
        - name: nginx-config-setup
          image: {{ .Values.images.nginx }}
          command:
            - /bin/sh
          args:
            - -c
            - |
              echo "Setting up nginx configuration for tenant {{ .Values.tenant.id }}"
              # Copy webapp files to nginx document root
              echo "Copying webapp files..."
              mkdir -p /storage/ArchAssets/public
              cp -r /tmp/webapp-files/* /storage/ArchAssets/public/ 2>/dev/null || true
              cp -r /tmp/webapp-files/. /storage/ArchAssets/public/ 2>/dev/null || true
              echo "Webapp files copied to nginx document root"
              ls -la /storage/ArchAssets/public/

              cat > /etc/nginx/conf.d/default.conf << 'EOF'
              server {
                  listen 8080;
                  server_name {{ .Values.tenant.domain }};
                  root /storage/ArchAssets/public;
                  index index.php index.html;
                  
                  # Enable gzip compression
                  gzip on;
                  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
                  
                  # Security headers
                  add_header X-Frame-Options "SAMEORIGIN" always;
                  add_header X-XSS-Protection "1; mode=block" always;
                  add_header X-Content-Type-Options "nosniff" always;
                  
                  location / {
                      try_files $uri $uri/ /index.php?$query_string;
                  }
                  
                  location ~ \.php$ {
                      fastcgi_pass {{ .Values.tenant.id }}-webapp:9000;
                      fastcgi_index index.php;
                      fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                      include fastcgi_params;
                      fastcgi_read_timeout 300;
                      fastcgi_buffer_size 128k;
                      fastcgi_buffers 4 256k;
                      fastcgi_busy_buffers_size 256k;
                  }
                  
                  location ~ /\.ht {
                      deny all;
                  }
                  
                  location /api/ {
                      try_files $uri $uri/ /api/index.php?$query_string;
                  }
                  
                  location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                      expires 1y;
                      add_header Cache-Control "public, immutable";
                  }
              }
              EOF
              echo "Nginx configuration created successfully"
              ls -la /etc/nginx/conf.d/
          volumeMounts:
            - mountPath: /etc/nginx/conf.d
              name: nginx-config
            - mountPath: /tmp/webapp-files
              name: webapp-files
      containers:
        - name: nginx
          image: {{ .Values.images.nginx }}
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          env:
            - name: TENANT_ID
              value: {{ .Values.tenant.id | quote }}
            - name: TENANT_DOMAIN
              value: {{ .Values.tenant.domain | quote }}
          volumeMounts:
            - mountPath: /etc/nginx/conf.d
              name: nginx-config
            - mountPath: /storage/clear
              name: s3-storage
            - mountPath: /storage/ArchAssets
              name: s3-storage
              subPath: ArchAssets
          {{- if .Values.nginx.resources }}
          {{- include "architrave-tenant.resources" (list . "nginx") | nindent 10 }}
          {{- end }}
          livenessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
      restartPolicy: Always
      volumes:
        - name: nginx-config
          emptyDir: {}
        - name: webapp-files
          emptyDir: {}
        {{- if .Values.storage.s3.enabled }}
        - name: s3-storage
          persistentVolumeClaim:
            claimName: {{ .Values.tenant.id }}-s3-pvc
        {{- else }}
        - name: s3-storage
          emptyDir: {}
        {{- end }}
      {{- with .Values.nginx.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nginx.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
