apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.tenant.id }}-webapp-env
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: webapp-env
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
data:
  # Advanced Uploader Configuration
  ADVANCED_UPLOADER_URL: "https://s3.eu-central-1.amazonaws.com/uploader.dev.core-sandbox.architrave.cloud/index.html"

  # API Configuration
  API_KEY_USER_EMAIL: "<EMAIL>"

  # Application Environment
  APP_ENVIRONMENT: {{ .Values.environment | default "production" | quote }}
  APP_HOST: {{ printf "https://%s" .Values.tenant.domain | quote }}

  # AV Automate Configuration
  AV_AUTOMATE_INSTANCE_LOCALE: "de_DE"
  AV_AUTOMATE_S3_BUCKET: {{ .Values.storage.s3.bucketName | default (printf "tenant-%s" .Values.tenant.id) | quote }}
  AV_AUTOMATE_S3_ENDPOINT: {{ printf "https://s3.%s.amazonaws.com" (.Values.storage.s3.region | default "eu-central-1") | quote }}
  AV_AUTOMATE_S3_REGION: {{ .Values.storage.s3.region | default "eu-central-1" | quote }}

  # Composer Configuration
  COMPOSER_MEMORY_LIMIT: "-1"

  # Customer Configuration
  CUSTOMER_ADMIN_EMAIL: "<EMAIL>"
  CUSTOMER_ID: {{ .Values.tenant.id | quote }}
  CUSTOMER_NAME: {{ .Values.tenant.name | default .Values.tenant.id | quote }}
  CUSTOMER_SUPPORT_EMAIL: "<EMAIL>"

  # DCM Configuration
  DCM_HOST: "dcm.architrave.de"
  DCM_PORT: "443"
  DCM_TRANSPORT: "https"

  # DQA Configuration
  DQA_LOCK_PERIOD_MINUTES: "30"

  # Elasticsearch Configuration
  ES_HOST: "elastic.local"
  ES_INDEX: "qa-5"

  # Global Search Configuration
  GLOBAL_SEARCH_CURL_REGION: "eu-central-1"

  # IMS Configuration
  IMS_LINKS: '[]'

  # LMC Configuration
  LMC_USER_PASSWORD_COST: "4"

  # Mandrill Configuration
  MANDRILL_KEY: ""

  # MySQL Database Configuration
  MYSQL_DATABASE: {{ printf "tenant_%s" (.Values.tenant.id | replace "-" "_") | quote }}
  MYSQL_HOST: {{ .Values.database.host | quote }}
  MYSQL_USER: {{ printf "user_%s" (.Values.tenant.id | replace "-" "_") | quote }}

  # MySQL SSL Configuration (required for Aurora with --require_secure_transport=ON)
  MYSQL_SSL_MODE: "REQUIRED"
  MYSQL_SSL_CA: "/tmp/rds-ca-2019-root.pem"
  MYSQL_SSL_VERIFY_SERVER_CERT: "false"

  # RabbitMQ Configuration
  RABBITMQ_HOST: {{ printf "%s-rabbitmq" .Values.tenant.id | quote }}
  RABBITMQ_USER: "guest"

  # Release Notes Configuration
  RELEASE_NOTES_PROVIDER: "hubsport"
  RELEASE_NOTES_URL: "https://example.com"

  # SCIM Configuration
  SCIM_USER_EMAIL: "<EMAIL>"

  # SSO Configuration
  SSO_PROVIDERS: '[]'

  # Trash Configuration
  TRASH_EXPIRATION_DAYS: "1"
  TRASH_EXPIRATION_DAYS_AV_AUTOMATE_APPROVED: "1"
  TRASH_EXPIRATION_DAYS_AV_AUTOMATE_DELETED: "1"

  # Cognito Configuration
  COGNITO_USER_POOL_ID: {{ .Values.cognito.userPoolId | quote }}
  COGNITO_CLIENT_ID: {{ .Values.cognito.clientId | quote }}
  COGNITO_CLIENT_SECRET: {{ .Values.cognito.clientSecret | quote }}
  COGNITO_REGION: {{ .Values.cognito.region | quote }}
  COGNITO_DOMAIN: {{ .Values.cognito.domain | quote }}
