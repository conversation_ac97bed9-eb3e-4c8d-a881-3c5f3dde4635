{{- if .Values.cronjobs.processDocuments.enabled }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ .Values.tenant.id }}-process-new-documents
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: process-new-documents
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  schedule: {{ .Values.cronjobs.processDocuments.schedule | quote }}
  concurrencyPolicy: {{ .Values.cronjobs.processDocuments.concurrencyPolicy }}
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            {{- include "architrave-tenant.selectorLabels" . | nindent 12 }}
            app: process-new-documents
          annotations:
            {{- with .Values.annotations }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
        spec:
          {{- with .Values.security.podSecurityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          initContainers:
          - name: ssl-cert-downloader
            image: curlimages/curl:latest
            command: ["/bin/sh"]
            args:
              - -c
              - |
                echo "Downloading RDS SSL certificate..."
                curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
                chmod 644 /tmp/rds-ca-2019-root.pem
                echo "RDS SSL certificate downloaded successfully"
                ls -la /tmp/
            env:
              - name: AWS_DEFAULT_REGION
                value: "eu-central-1"
            volumeMounts:
              - mountPath: /tmp
                name: ssl-cert-volume
          containers:
          - name: webapp
            image: {{ .Values.images.webapp }}
            imagePullPolicy: {{ .Values.images.pullPolicy }}
            {{- with .Values.security.securityContext }}
            securityContext:
              {{- toYaml . | nindent 14 }}
            {{- end }}
            command: {{ .Values.cronjobs.processDocuments.command | toJson }}
            args: {{ .Values.cronjobs.processDocuments.args | toJson }}
            env:
              {{- include "architrave-tenant.commonEnv" . | nindent 14 }}
            envFrom:
              - configMapRef:
                  name: {{ .Values.tenant.id }}-webapp-env
              - secretRef:
                  name: {{ .Values.tenant.id }}-webapp-secrets
            volumeMounts:
              {{- include "architrave-tenant.commonVolumeMounts" . | nindent 14 }}
              - mountPath: /tmp
                name: ssl-cert-volume
            {{- include "architrave-tenant.resources" (list . "queues") | nindent 12 }}
          restartPolicy: OnFailure
          volumes:
            {{- include "architrave-tenant.commonVolumes" . | nindent 12 }}
            - name: ssl-cert-volume
              emptyDir: {}
          {{- with .Values.nodeSelector }}
          nodeSelector:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.affinity }}
          affinity:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.tolerations }}
          tolerations:
            {{- toYaml . | nindent 12 }}
          {{- end }}
{{- end }}
