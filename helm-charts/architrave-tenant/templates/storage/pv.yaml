{{- if .Values.storage.s3.enabled }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: {{ .Values.tenant.id }}-s3-pv
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  capacity:
    storage: {{ .Values.storage.s3.size }}
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: {{ .Values.tenant.id }}-s3-sc
  {{- if .Values.storage.s3.mountOptions }}
  mountOptions:
    {{- toYaml .Values.storage.s3.mountOptions | nindent 4 }}
  {{- end }}
  csi:
    driver: s3.csi.aws.com
    volumeHandle: {{ .Values.tenant.id }}-s3-csi-vol
    volumeAttributes:
      bucketName: {{ include "architrave-tenant.s3BucketName" . }}
      region: {{ .Values.storage.s3.region }}
{{- end }}
