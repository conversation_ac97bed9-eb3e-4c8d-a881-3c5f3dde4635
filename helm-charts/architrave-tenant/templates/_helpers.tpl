{{/*
Expand the name of the chart.
*/}}
{{- define "architrave-tenant.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
*/}}
{{- define "architrave-tenant.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "architrave-tenant.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "architrave-tenant.labels" -}}
helm.sh/chart: {{ include "architrave-tenant.chart" . }}
{{ include "architrave-tenant.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
tenant: {{ .Values.tenant.id }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "architrave-tenant.selectorLabels" -}}
app.kubernetes.io/name: {{ include "architrave-tenant.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "architrave-tenant.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "architrave-tenant.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Generate tenant namespace
*/}}
{{- define "architrave-tenant.namespace" -}}
{{- printf "tenant-%s" .Values.tenant.id }}
{{- end }}

{{/*
Generate S3 bucket name
*/}}
{{- define "architrave-tenant.s3BucketName" -}}
{{- if .Values.storage.s3.bucketName }}
{{- .Values.storage.s3.bucketName }}
{{- else }}
{{- printf "tenant-%s-storage" .Values.tenant.id }}
{{- end }}
{{- end }}

{{/*
Generate common environment variables
*/}}
{{- define "architrave-tenant.commonEnv" -}}
- name: TENANT_ID
  value: {{ .Values.tenant.id | quote }}
- name: TENANT_DOMAIN
  value: {{ .Values.tenant.domain | quote }}
- name: S3_BUCKET_NAME
  value: {{ include "architrave-tenant.s3BucketName" . | quote }}
- name: AWS_REGION
  value: {{ .Values.storage.s3.region | quote }}
{{- end }}

{{/*
Generate common volume mounts
*/}}
{{- define "architrave-tenant.commonVolumeMounts" -}}
{{- if .Values.storage.s3.enabled }}
- mountPath: /storage/clear
  name: s3-storage
{{- end }}
- mountPath: /storage/ArchAssets/data/DoctrineORMModule
  name: doctrine-proxies
{{- end }}

{{/*
Generate common volumes
*/}}
{{- define "architrave-tenant.commonVolumes" -}}
- name: doctrine-proxies
  emptyDir: {}
{{- if .Values.storage.s3.enabled }}
- name: s3-storage
  persistentVolumeClaim:
    claimName: {{ printf "%s-s3-pvc" .Values.tenant.id }}
{{- else }}
- name: s3-storage
  emptyDir: {}
{{- end }}
{{- end }}

{{/*
Generate resource limits
*/}}
{{- define "architrave-tenant.resources" -}}
{{- $root := index . 0 -}}
{{- $component := index . 1 -}}
{{- if hasKey $root.Values.resources $component }}
resources:
  {{- toYaml (index $root.Values.resources $component) | nindent 2 }}
{{- end }}
{{- end }}
