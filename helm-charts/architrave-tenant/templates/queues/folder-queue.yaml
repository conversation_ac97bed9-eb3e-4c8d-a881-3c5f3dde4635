{{- if .Values.queues.folder.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.tenant.id }}-folder-queue
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: folder-queue
  annotations:
    owner: architrave
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.queues.folder.replicas }}
  selector:
    matchLabels:
      {{- include "architrave-tenant.selectorLabels" . | nindent 6 }}
      app: folder-queue
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        {{- include "architrave-tenant.selectorLabels" . | nindent 8 }}
        app: folder-queue
      annotations:
        owner: architrave
        {{- with .Values.annotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.security.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      initContainers:
        - name: ssl-cert-downloader
          image: curlimages/curl:latest
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Downloading RDS SSL certificate..."
              curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
              chmod 644 /tmp/rds-ca-2019-root.pem
              echo "RDS SSL certificate downloaded successfully"
              ls -la /tmp/
          env:
            - name: AWS_DEFAULT_REGION
              value: "eu-central-1"
          volumeMounts:
            - mountPath: /tmp
              name: ssl-cert-volume
      containers:
        - name: folder-queue
          image: {{ .Values.images.webapp }}
          imagePullPolicy: {{ .Values.images.pullPolicy }}
          {{- with .Values.security.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          command: {{ .Values.queues.folder.command | toJson }}
          args: {{ .Values.queues.folder.args | toJson }}
          env:
            {{- include "architrave-tenant.commonEnv" . | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ .Values.tenant.id }}-webapp-env
            - secretRef:
                name: {{ .Values.tenant.id }}-webapp-secrets
          volumeMounts:
            {{- include "architrave-tenant.commonVolumeMounts" . | nindent 12 }}
            - mountPath: /tmp
              name: ssl-cert-volume
          {{- include "architrave-tenant.resources" (list . "queues") | nindent 10 }}
          livenessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - "test -f /tmp/queue_running || exit 1"
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - "test -f /tmp/queue_running || exit 1"
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
      volumes:
        {{- include "architrave-tenant.commonVolumes" . | nindent 8 }}
        - name: ssl-cert-volume
          emptyDir: {}
      restartPolicy: Always
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
---
{{- if .Values.queues.folder.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.tenant.id }}-folder-queue
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: folder-queue
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.service.type }}
  selector:
    {{- include "architrave-tenant.selectorLabels" . | nindent 4 }}
    app: folder-queue
  ports:
    - protocol: TCP
      port: 9000
      targetPort: 9000
{{- end }}
{{- end }}
