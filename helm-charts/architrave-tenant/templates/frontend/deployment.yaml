{{- if .Values.frontend.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.tenant.id }}-frontend
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: {{ .Values.tenant.id }}-frontend
    component: frontend
spec:
  replicas: {{ .Values.frontend.replicas }}
  selector:
    matchLabels:
      {{- include "architrave-tenant.selectorLabels" . | nindent 6 }}
      app: {{ .Values.tenant.id }}-frontend
      component: frontend
  template:
    metadata:
      labels:
        {{- include "architrave-tenant.selectorLabels" . | nindent 8 }}
        app: {{ .Values.tenant.id }}-frontend
        component: frontend
        tenant: {{ .Values.tenant.id }}
    spec:
      securityContext:
        runAsUser: 101
        runAsGroup: 101
        fsGroup: 101
      initContainers:
        - name: webapp-files-copier
          image: {{ .Values.images.webapp }}
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Copying webapp files to shared volume..."
              cp -r /storage/ArchAssets/public/* /webapp-files/ 2>/dev/null || true
              cp -r /storage/ArchAssets/public/. /webapp-files/ 2>/dev/null || true
              echo "Webapp files copied successfully"
              ls -la /webapp-files/
          volumeMounts:
            - mountPath: /webapp-files
              name: webapp-files
        - name: nginx-config-setup
          image: nginx:1.21-alpine
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Creating nginx configuration for frontend..."
              cat > /nginx-config/nginx.conf << 'EOF'
              pid /tmp/nginx.pid;
              events {
                  worker_connections 1024;
              }
              http {
                  include /etc/nginx/mime.types;
                  default_type application/octet-stream;
                  sendfile on;
                  keepalive_timeout 65;

                  server {
                      listen 8080;
                      server_name {{ .Values.tenant.domain }};

                  # Health check endpoint
                  location = /health {
                      access_log off;
                      return 200 "healthy\n";
                      add_header Content-Type text/plain;
                  }

                  # Serve static files from the webapp
                  location / {
                      root /webapp-files;
                      index index.html index.php;
                      try_files $uri $uri/ /index.html;
                      
                      # Cache static assets
                      location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                          expires 1y;
                          add_header Cache-Control "public, immutable";
                      }
                  }
                  
                  # Proxy API requests to backend
                  location /api/ {
                      proxy_pass http://{{ .Values.tenant.id }}-webapp:8080;
                      proxy_set_header Host $host;
                      proxy_set_header X-Real-IP $remote_addr;
                      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                      proxy_set_header X-Forwarded-Proto $scheme;
                      proxy_connect_timeout 30s;
                      proxy_send_timeout 30s;
                      proxy_read_timeout 30s;
                  }
                  
                  # Proxy LmcUser authentication requests to backend API
                  location /user/ {
                      proxy_pass http://{{ .Values.tenant.id }}-webapp:8080/api/user/;
                      proxy_set_header Host $host;
                      proxy_set_header X-Real-IP $remote_addr;
                      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                      proxy_set_header X-Forwarded-Proto $scheme;
                      proxy_connect_timeout 30s;
                      proxy_send_timeout 30s;
                      proxy_read_timeout 30s;
                  }
                  
                  # Health check endpoint
                  location /health {
                      proxy_pass http://{{ .Values.tenant.id }}-webapp:8080;
                      proxy_set_header Host $host;
                      proxy_set_header X-Real-IP $remote_addr;
                      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                      proxy_set_header X-Forwarded-Proto $scheme;
                  }
              }
              }
              EOF
              echo "Nginx configuration created successfully"
          volumeMounts:
            - mountPath: /nginx-config
              name: nginx-config
        - name: nginx-cache-setup
          image: nginx:1.21-alpine
          securityContext:
            runAsUser: 0
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Creating nginx cache directories..."
              mkdir -p /var/cache/nginx/client_temp
              mkdir -p /var/cache/nginx/proxy_temp
              mkdir -p /var/cache/nginx/fastcgi_temp
              mkdir -p /var/cache/nginx/uwsgi_temp
              mkdir -p /var/cache/nginx/scgi_temp
              chown -R 101:101 /var/cache/nginx
              chmod -R 755 /var/cache/nginx
              echo "Nginx cache directories created successfully"
          volumeMounts:
            - mountPath: /var/cache/nginx
              name: nginx-cache
      containers:
        - name: frontend
          image: {{ .Values.images.nginx }}
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          env:
            - name: TENANT_ID
              value: {{ .Values.tenant.id | quote }}
            - name: DOMAIN
              value: {{ .Values.tenant.domain | quote }}
            - name: ENVIRONMENT
              value: "production"
            - name: LANGUAGE
              value: "en"
          {{- if .Values.frontend.resources }}
          {{- include "architrave-tenant.resources" (list . "frontend") | nindent 10 }}
          {{- end }}
          volumeMounts:
            - mountPath: /etc/nginx/nginx.conf
              name: nginx-config
              subPath: nginx.conf
            - mountPath: /webapp-files
              name: webapp-files
            - mountPath: /var/cache/nginx
              name: nginx-cache
          {{- with .Values.frontend.livenessProbe }}
          livenessProbe:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.frontend.readinessProbe }}
          readinessProbe:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      volumes:
        - name: nginx-config
          emptyDir: {}
        - name: webapp-files
          emptyDir: {}
        - name: nginx-cache
          emptyDir: {}
{{- end }}
