{{- if .Values.webapp.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.tenant.id }}-webapp
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: webapp
    component: webapp
  annotations:
    owner: architrave
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.webapp.replicas }}
  selector:
    matchLabels:
      {{- include "architrave-tenant.selectorLabels" . | nindent 6 }}
      app: webapp
      component: webapp
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        {{- include "architrave-tenant.selectorLabels" . | nindent 8 }}
        app: webapp
        component: webapp
      annotations:
        owner: architrave
        {{- with .Values.annotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      securityContext:
        fsGroup: 33
      initContainers:
        - name: nginx-config-setup
          image: busybox:latest
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Setting up nginx configuration for HTTP on port 8080..."
              cat > /etc/nginx/conf.d/default.conf << 'EOF'
              server {
                  listen 8080;
                  server_name localhost;
                  root /storage/ArchAssets/public;
                  index index.php index.html;

                  location / {
                      try_files $uri $uri/ /index.php?$query_string;
                  }

                  location ~ \.php$ {
                      fastcgi_pass 127.0.0.1:9000;
                      fastcgi_index index.php;
                      fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                      include fastcgi_params;
                      fastcgi_read_timeout 300;
                      fastcgi_buffer_size 128k;
                      fastcgi_buffers 4 256k;
                      fastcgi_busy_buffers_size 256k;
                  }

                  location ~ /\.ht {
                      deny all;
                  }

                  location /api/ {
                      try_files $uri $uri/ /api/index.php?$query_string;
                  }

                  location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                      expires 1y;
                      add_header Cache-Control "public, immutable";
                  }
              }
              EOF
              echo "Nginx configuration created successfully"
          volumeMounts:
            - mountPath: /etc/nginx/conf.d
              name: nginx-config-volume
        - name: ssl-cert-downloader
          image: curlimages/curl:latest
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Downloading RDS SSL certificate..."
              curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
              chmod 644 /tmp/rds-ca-2019-root.pem
              echo "RDS SSL certificate downloaded successfully"
              ls -la /tmp/
          env:
            - name: AWS_DEFAULT_REGION
              value: "eu-central-1"
          volumeMounts:
            - mountPath: /tmp
              name: ssl-cert-volume
        - name: local-php-config-setup
          image: {{ .Values.images.webapp }}
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Setting up local.php configuration..."
              mkdir -p /storage/clear/config/autoload
              cat > /storage/clear/config/autoload/local.php << 'EOF'
              <?php
              return [
                  'doctrine' => [
                      'connection' => [
                          'orm_default' => [
                              'driverClass' => 'Doctrine\DBAL\Driver\PDOMySql\Driver',
                              'params' => [
                                  'host' => getenv('MYSQL_HOST') ?: 'localhost',
                                  'port' => getenv('MYSQL_PORT') ?: '3306',
                                  'user' => getenv('MYSQL_USER') ?: 'admin',
                                  'password' => getenv('MYSQL_PASSWORD') ?: '',
                                  'dbname' => getenv('MYSQL_DATABASE') ?: 'tenant_database',
                                  'charset' => 'utf8mb4',
                                  'driverOptions' => [
                                      PDO::MYSQL_ATTR_SSL_CA => '/tmp/rds-ca-2019-root.pem',
                                      PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                                      PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
                                  ]
                              ]
                          ]
                      ]
                  ],
                  'cognito' => [
                      'user_pool_id' => getenv('COGNITO_USER_POOL_ID') ?: '',
                      'client_id' => getenv('COGNITO_CLIENT_ID') ?: '',
                      'client_secret' => getenv('COGNITO_CLIENT_SECRET') ?: '',
                      'region' => getenv('COGNITO_REGION') ?: 'eu-central-1',
                      'domain' => getenv('COGNITO_DOMAIN') ?: ''
                  ],
                  'directories' => [
                      'assets' => '/storage/ArchAssets',
                      'tmp' => '/tmp',
                      'quarantine' => '/storage/ArchAssets/quarantine'
                  ]
              ];
              EOF
              echo "Creating required directories..."
              mkdir -p /storage/ArchAssets/public
              mkdir -p /storage/ArchAssets/data
              mkdir -p /storage/ArchAssets/quarantine
              chmod -R 777 /storage/ArchAssets
              chown -R www-data:www-data /storage/ArchAssets
              echo "local.php configuration created successfully"
              ls -la /storage/clear/config/autoload/
          envFrom:
            - configMapRef:
                name: {{ .Values.tenant.id }}-webapp-env
            - secretRef:
                name: {{ .Values.tenant.id }}-webapp-secrets
          volumeMounts:
            - mountPath: /storage/clear
              name: s3-storage


      containers:
        - name: webapp
          image: {{ .Values.images.webapp }}
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Starting PHP-FPM for tenant {{ .Values.tenant.id }}"
              # Start PHP-FPM in foreground mode
              exec php-fpm --nodaemonize --force-stderr
          envFrom:
            - configMapRef:
                name: {{ .Values.tenant.id }}-webapp-env
            - secretRef:
                name: {{ .Values.tenant.id }}-webapp-secrets
          ports:
            - containerPort: 9000
              name: php-fpm
              protocol: TCP
          {{- if .Values.webapp.resources }}
          {{- include "architrave-tenant.resources" (list . "webapp") | nindent 10 }}
          {{- end }}
          volumeMounts:
            - mountPath: /storage/ArchAssets
              name: s3-storage
            - mountPath: /storage/ArchAssets/data/DoctrineORMModule
              name: doctrine-proxies
            - name: previewchunkedsprites
              mountPath: /storage/ArchAssets/public/assets/previewChunkedSprites
            - mountPath: /tmp
              name: ssl-cert-volume

          livenessProbe:
            tcpSocket:
              port: 9000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: 9000
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
        - name: nginx
          image: {{ .Values.images.nginx }}
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          env:
            - name: TENANT_ID
              value: {{ .Values.tenant.id | quote }}
            - name: TENANT_DOMAIN
              value: {{ .Values.tenant.domain | quote }}
          volumeMounts:
            - mountPath: /etc/nginx/conf.d
              name: nginx-config-volume
            - mountPath: /storage/ArchAssets
              name: s3-storage
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3

      restartPolicy: Always
      volumes:
        - name: doctrine-proxies
          emptyDir: {}
        {{- if .Values.storage.s3.enabled }}
        - name: s3-storage
          persistentVolumeClaim:
            claimName: {{ .Values.tenant.id }}-s3-pvc
        {{- else }}
        - name: s3-storage
          emptyDir: {}
        {{- end }}
        - name: previewchunkedsprites
          emptyDir: {}
        - name: ssl-cert-volume
          emptyDir: {}
        - name: nginx-config-volume
          emptyDir: {}
      {{- with .Values.webapp.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.webapp.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
