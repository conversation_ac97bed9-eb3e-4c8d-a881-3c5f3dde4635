# Production values for architrave-tenant
# Override default values for production environment

# Container images - production versions
images:
  webapp: "545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v25"
  nginx: "545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.8-preview-2"
  rabbitmq: "545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02"
  pullPolicy: IfNotPresent

# Production resource limits
resources:
  webapp:
    requests:
      cpu: "500m"
      memory: "1Gi"
    limits:
      cpu: "2000m"
      memory: "2Gi"
  queues:
    requests:
      cpu: "200m"
      memory: "512Mi"
    limits:
      cpu: "1000m"
      memory: "1Gi"
  rabbitmq:
    requests:
      cpu: "200m"
      memory: "512Mi"
    limits:
      cpu: "1000m"
      memory: "1Gi"

# Production storage configuration
storage:
  s3:
    enabled: true
    region: "eu-central-1"
    size: "50Gi"
    storageClass: "s3-sc"

# Production CronJob schedules (more frequent)
cronjobs:
  processDocuments:
    enabled: true
    schedule: "*/3 * * * *"  # Every 3 minutes
  processStaged:
    enabled: true
    schedule: "*/10 * * * *"  # Every 10 minutes
  reprocess:
    enabled: true
    schedule: "*/5 * * * *"   # Every 5 minutes

# Production queue scaling
queues:
  default:
    enabled: true
    replicas: 2
  folder:
    enabled: true
    replicas: 2
  notification:
    enabled: true
    replicas: 1

# Production webapp scaling
webapp:
  enabled: true
  replicas: 2

nginx:
  enabled: true
  replicas: 2

# Production RabbitMQ
rabbitmq:
  enabled: true
  replicas: 1
  management:
    enabled: true

# Production monitoring
monitoring:
  enabled: true
  healthCheck:
    enabled: true
    initialDelaySeconds: 60
    periodSeconds: 30

# Production autoscaling
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Production security
security:
  podSecurityContext:
    runAsUser: 33
    runAsGroup: 33
    fsGroup: 33
    runAsNonRoot: true
  securityContext:
    allowPrivilegeEscalation: false
    runAsNonRoot: true
    runAsUser: 33
    readOnlyRootFilesystem: false
    capabilities:
      drop:
        - ALL

# Production environment
environment: "production"

# Production node selection (if needed)
nodeSelector:
  node-type: "worker"

# Production tolerations for dedicated nodes
tolerations:
  - key: "dedicated"
    operator: "Equal"
    value: "architrave"
    effect: "NoSchedule"

# Production affinity rules
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - architrave-tenant
        topologyKey: kubernetes.io/hostname
