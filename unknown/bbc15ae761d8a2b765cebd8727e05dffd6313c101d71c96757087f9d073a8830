{{- if .Values.storage.s3.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Values.tenant.id }}-s3-pvc
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: {{ .Values.tenant.id }}-s3-sc
  resources:
    requests:
      storage: {{ .Values.storage.s3.size }}
{{- end }}
