# Use imported table if it exists, otherwise create a new one
locals {
  import_existing_resources = var.import_existing_resources
}

resource "aws_dynamodb_table" "tenant_mapping" {
  name         = "${var.environment}-tenant-mapping"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "tenant_id"

  attribute {
    name = "tenant_id"
    type = "S"
  }

  point_in_time_recovery {
    enabled = true
  }

  server_side_encryption {
    enabled = true
    # DISABLED: kms_key_arn = null # KMS keys removed to avoid deletion protection
  }

  tags = var.tags
}

resource "aws_iam_role" "dynamodb_access_role" {
  name = "${var.environment}-dynamodb-access-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRoleWithWebIdentity"
      Effect = "Allow"
      Principal = {
        Federated = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${replace(var.eks_oidc_provider_url, "https://", "")}"
      }
      Condition = {
        StringEquals = {
          "${replace(var.eks_oidc_provider_url, "https://", "")}:aud" = "sts.amazonaws.com"
          "${replace(var.eks_oidc_provider_url, "https://", "")}:sub" = "system:serviceaccount:${var.namespace}:${var.serviceaccount}"
        }
      }
    }]
  })
  tags = merge(var.tags, { Name = "${var.environment}-dynamodb-access-role" })
}

resource "aws_iam_policy" "dynamodb_access_policy" {
  name        = "${var.environment}-dynamodb-access-policy"
  description = "Policy for accessing DynamoDB table"
  policy      = data.aws_iam_policy_document.dynamodb_access_policy_document.json
}

data "aws_iam_policy_document" "dynamodb_access_policy_document" {
  statement {
    actions   = ["dynamodb:Query", "dynamodb:Scan", "dynamodb:GetItem", "dynamodb:PutItem", "dynamodb:UpdateItem", "dynamodb:DeleteItem"]
    effect    = "Allow"
    resources = [aws_dynamodb_table.tenant_mapping.arn]
  }
}

resource "aws_iam_role_policy_attachment" "dynamodb_access_policy_attachment" {
  role       = aws_iam_role.dynamodb_access_role.name
  policy_arn = aws_iam_policy.dynamodb_access_policy.arn
}

data "aws_caller_identity" "current" {}
data "aws_region" "current" {}