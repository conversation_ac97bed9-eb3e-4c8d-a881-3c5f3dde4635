variable "environment" {
  description = "Deployment Environment"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "alert_emails" {
  description = "List of email addresses to receive GuardDuty findings alerts"
  type        = list(string)
  default     = []
}

variable "finding_publishing_frequency" {
  description = "Frequency of GuardDuty findings publishing"
  type        = string
  default     = "FIFTEEN_MINUTES"
  validation {
    condition     = contains(["FIFTEEN_MINUTES", "ONE_HOUR", "SIX_HOURS"], var.finding_publishing_frequency)
    error_message = "Valid values for finding_publishing_frequency are FIFTEEN_MINUTES, ONE_HOUR, or SIX_HOURS."
  }
}

variable "skip_publishing_destination" {
  description = "Whether to skip creating the GuardDuty publishing destination"
  type        = bool
  default     = false
}

variable "kms_key_arn" {
  description = "ARN of the KMS key to use for encrypting GuardDuty findings"
  type        = string
  default     = null
}
