resource "aws_guardduty_detector" "main" {
  enable                       = true
  finding_publishing_frequency = "FIFTEEN_MINUTES"

  datasources {
    s3_logs {
      enable = true
    }
    kubernetes {
      audit_logs {
        enable = true
      }
    }
    malware_protection {
      scan_ec2_instance_with_findings {
        ebs_volumes {
          enable = true
        }
      }
    }
  }

  tags = var.tags
}

# Enable Lambda Network Logs (GuardDuty.6)
resource "aws_guardduty_detector_feature" "lambda_network_logs" {
  detector_id = aws_guardduty_detector.main.id
  name        = "LAMBDA_NETWORK_LOGS"
  status      = "ENABLED"
}

# Enable Runtime Monitoring (GuardDuty.11)
resource "aws_guardduty_detector_feature" "runtime_monitoring" {
  detector_id = aws_guardduty_detector.main.id
  name        = "RUNTIME_MONITORING"
  status      = "ENABLED"
}

resource "aws_guardduty_publishing_destination" "main" {
  count           = var.skip_publishing_destination ? 0 : 1
  detector_id     = aws_guardduty_detector.main.id
  destination_arn = aws_s3_bucket.guardduty_findings.arn
  kms_key_arn     = var.kms_key_arn
}

resource "aws_s3_bucket" "guardduty_findings" {
  bucket        = "${var.environment}-guardduty-findings-${data.aws_caller_identity.current.account_id}"
  force_destroy = true # Enable force destroy for easier cleanup during development

  tags = var.tags
}

resource "aws_s3_bucket_versioning" "guardduty_findings_versioning" {
  bucket = aws_s3_bucket.guardduty_findings.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Add logging to the bucket itself
resource "aws_s3_bucket_logging" "guardduty_findings_logging" {
  bucket = aws_s3_bucket.guardduty_findings.id

  target_bucket = aws_s3_bucket.guardduty_findings.id
  target_prefix = "log/"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "guardduty_bucket_encryption" {
  bucket = aws_s3_bucket.guardduty_findings.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = var.kms_key_arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "guardduty_bucket_public_access" {
  bucket = aws_s3_bucket.guardduty_findings.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_policy" "guardduty_findings_policy" {
  bucket = aws_s3_bucket.guardduty_findings.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowGuardDutyToPutObjects"
        Effect = "Allow"
        Principal = {
          Service = "guardduty.amazonaws.com"
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.guardduty_findings.arn}/*"
        Condition = {
          StringEquals = {
            "aws:SourceAccount" = data.aws_caller_identity.current.account_id
            "aws:SourceArn"     = "arn:aws:guardduty:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:detector/${aws_guardduty_detector.main.id}"
          }
        }
      },
      {
        Sid    = "AllowGuardDutyToGetBucketLocation"
        Effect = "Allow"
        Principal = {
          Service = "guardduty.amazonaws.com"
        }
        Action   = "s3:GetBucketLocation"
        Resource = aws_s3_bucket.guardduty_findings.arn
        Condition = {
          StringEquals = {
            "aws:SourceAccount" = data.aws_caller_identity.current.account_id
            "aws:SourceArn"     = "arn:aws:guardduty:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:detector/${aws_guardduty_detector.main.id}"
          }
        }
      },
      {
        Sid       = "RequireSSLOnly"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.guardduty_findings.arn,
          "${aws_s3_bucket.guardduty_findings.arn}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      }
    ]
  })
}

# KMS keys have been disabled to avoid deletion protection
# resource "aws_kms_key" "guardduty_key" {
#   description             = "KMS key for GuardDuty findings encryption"
#   deletion_window_in_days = 30
#   enable_key_rotation     = true
#
#   policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Sid    = "Enable IAM User Permissions"
#         Effect = "Allow"
#         Principal = {
#           AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
#         }
#         Action   = "kms:*"
#         Resource = "*"
#       },
#       {
#         Sid    = "Allow GuardDuty to use the key"
#         Effect = "Allow"
#         Principal = {
#           Service = "guardduty.amazonaws.com"
#         }
#         Action = [
#           "kms:GenerateDataKey",
#           "kms:Encrypt",
#           "kms:Decrypt"
#         ]
#         Resource = "*"
#         Condition = {
#           StringEquals = {
#             "aws:SourceAccount" = data.aws_caller_identity.current.account_id
#             "aws:SourceArn"     = "arn:aws:guardduty:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:detector/${aws_guardduty_detector.main.id}"
#           }
#         }
#       }
#     ]
#   })
#
#   tags = var.tags
# }

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Use the KMS key ARN passed as a variable
locals {
  security_key_arn = var.kms_key_arn
}