resource "aws_ecr_repository" "app_repository" {
  name                 = "${var.environment}-${var.repository_name}"
  image_tag_mutability = "IMMUTABLE" # Enforce immutable tags for better security

  image_scanning_configuration {
    scan_on_push = true # Enable automatic scanning on image push
  }

  encryption_configuration {
    encryption_type = "AES256"
    # DISABLED: kms_key = var.kms_key_arn # KMS keys removed to avoid deletion protection
  }

  tags = merge(var.tags, {
    Name        = "${var.environment}-${var.repository_name}",
    Environment = var.environment
  })
}

# ECR Repository Policy to restrict access
resource "aws_ecr_repository_policy" "app_repository_policy" {
  repository = aws_ecr_repository.app_repository.name
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "LimitECRAccess",
        Effect = "Allow",
        Principal = {
          AWS = var.allowed_account_ids
        },
        Action = [
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:BatchCheckLayerAvailability",
          "ecr:PutImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload"
        ]
      }
    ]
  })
}

# ECR Lifecycle Policy to manage image retention
resource "aws_ecr_lifecycle_policy" "app_repository_lifecycle" {
  repository = aws_ecr_repository.app_repository.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1,
        description  = "Keep only the last 10 images",
        selection = {
          tagStatus   = "any",
          countType   = "imageCountMoreThan",
          countNumber = 10
        },
        action = {
          type = "expire"
        }
      }
    ]
  })
}
