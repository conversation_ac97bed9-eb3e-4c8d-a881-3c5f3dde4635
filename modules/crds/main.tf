# Centralized CRD installation module
# Install CRDs using remote Kubernetes manifests (no local-exec)

variable "install_keda_crds" {
  description = "Install KEDA CRDs"
  type        = bool
  default     = true
}

variable "install_karpenter_crds" {
  description = "Install Karpenter CRDs"
  type        = bool
  default     = true
}

variable "keda_version" {
  description = "KEDA version for CRDs"
  type        = string
  default     = "2.17.2"
}

variable "karpenter_version" {
  description = "Karpenter version for CRDs"
  type        = string
  default     = "1.0.0"
}

# Install KEDA CRDs using remote HTTP data source
data "http" "keda_crds" {
  count = var.install_keda_crds ? 1 : 0
  url   = "https://github.com/kedacore/keda/releases/download/v${var.keda_version}/keda-${var.keda_version}-crds.yaml"
}

# Apply KEDA CRDs using kubectl provider
resource "kubectl_manifest" "keda_crds" {
  count     = var.install_keda_crds ? 1 : 0
  yaml_body = data.http.keda_crds[0].response_body
  
  server_side_apply = true
  force_conflicts   = true
}

# Install Karpenter CRDs using remote HTTP data sources
data "http" "karpenter_nodeclaims_crd" {
  count = var.install_karpenter_crds ? 1 : 0
  url   = "https://raw.githubusercontent.com/aws/karpenter-provider-aws/v${var.karpenter_version}/pkg/apis/crds/karpenter.sh_nodeclaims.yaml"
}

data "http" "karpenter_nodepools_crd" {
  count = var.install_karpenter_crds ? 1 : 0
  url   = "https://raw.githubusercontent.com/aws/karpenter-provider-aws/v${var.karpenter_version}/pkg/apis/crds/karpenter.sh_nodepools.yaml"
}

data "http" "karpenter_ec2nodeclasses_crd" {
  count = var.install_karpenter_crds ? 1 : 0
  url   = "https://raw.githubusercontent.com/aws/karpenter-provider-aws/v${var.karpenter_version}/pkg/apis/crds/karpenter.k8s.aws_ec2nodeclasses.yaml"
}

resource "kubectl_manifest" "karpenter_nodeclaims_crd" {
  count     = var.install_karpenter_crds ? 1 : 0
  yaml_body = data.http.karpenter_nodeclaims_crd[0].response_body
  
  server_side_apply = true
  force_conflicts   = true
}

resource "kubectl_manifest" "karpenter_nodepools_crd" {
  count     = var.install_karpenter_crds ? 1 : 0
  yaml_body = data.http.karpenter_nodepools_crd[0].response_body
  
  server_side_apply = true
  force_conflicts   = true
}

resource "kubectl_manifest" "karpenter_ec2nodeclasses_crd" {
  count     = var.install_karpenter_crds ? 1 : 0
  yaml_body = data.http.karpenter_ec2nodeclasses_crd[0].response_body
  
  server_side_apply = true
  force_conflicts   = true
}

output "keda_crds_installed" {
  value = var.install_keda_crds
}

output "karpenter_crds_installed" {
  value = var.install_karpenter_crds
}
