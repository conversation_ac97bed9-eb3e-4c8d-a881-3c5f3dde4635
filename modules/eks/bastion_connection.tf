# Bastion connection configuration for EKS cluster
# This file handles connectivity between the bastion host and EKS cluster

# Skip connectivity check in CI/CD environments
resource "null_resource" "wait_for_kubernetes_api" {
  count = var.skip_eks_connectivity_check ? 0 : 1

  depends_on = [
    aws_eks_cluster.this,
    time_sleep.wait_for_cluster
  ]

  triggers = {
    cluster_endpoint = local.cluster_endpoint
  }

  # Simple check that works in both local and CI/CD environments
  provisioner "local-exec" {
    command = <<-EOT
      echo "Waiting for EKS API at ${local.cluster_endpoint} to become available..."
      # Check if curl is available
      if command -v curl >/dev/null 2>&1; then
        # This script works with both public and private endpoints
        for i in {1..10}; do
          echo "Attempt $i/10..."
          if curl -s --max-time 5 -k ${local.cluster_endpoint} > /dev/null; then
            echo "EKS API is reachable!"
            exit 0
          fi
          echo "EKS API not yet available, waiting..."
          sleep 10
        done
      else
        echo "curl command not found. Skipping API availability check."
      fi
      echo "Warning: Could not confirm EKS API availability. Continuing anyway..."
      exit 0
    EOT
  }
}

# Create a flag to indicate EKS is ready using null_resource instead of local_file
resource "null_resource" "eks_ready" {
  count = var.skip_eks_connectivity_check ? 0 : 1
  depends_on = [
    null_resource.wait_for_kubernetes_api
  ]

  triggers = {
    cluster_name = var.cluster_name
  }

  provisioner "local-exec" {
    command = "echo 'EKS cluster ${var.cluster_name} is ready' > ${path.module}/.eks_ready"
  }
}
