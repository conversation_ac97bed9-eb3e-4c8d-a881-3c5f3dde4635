# Consolidated EKS Module Outputs - All module outputs should be defined here

output "cluster_endpoint" {
  description = "The endpoint for the EKS cluster API server"
  value       = local.cluster_endpoint
}

output "cluster_ca_certificate" {
  description = "The certificate authority data for the EKS cluster"
  value       = local.cluster_ca_certificate
  sensitive   = true
}

output "cluster_auth_token" {
  description = "Authentication token for EKS cluster"
  value       = local.cluster_auth_token
  sensitive   = true
}

output "cluster_id" {
  description = "The ID of the EKS cluster"
  value       = local.cluster_exists ? data.aws_eks_cluster.check_exists[0].id : (var.create_eks ? try(aws_eks_cluster.this[0].id, "") : try(data.aws_eks_cluster.existing[0].id, ""))
}

output "cluster_name" {
  description = "The name of the EKS cluster"
  value       = var.cluster_name
}

output "cluster_arn" {
  description = "ARN of the EKS cluster"
  value       = local.cluster_exists ? data.aws_eks_cluster.check_exists[0].arn : (var.create_eks ? try(aws_eks_cluster.this[0].arn, "") : try(data.aws_eks_cluster.existing[0].arn, ""))
}

output "cluster_security_group_id" {
  description = "The security group ID of the EKS cluster"
  value       = var.create_security_groups ? aws_security_group.eks_cluster[0].id : ""
}

output "node_security_group_id" {
  description = "The security group ID of the EKS node groups"
  value       = var.create_security_groups ? aws_security_group.eks_node_group[0].id : ""
}

output "oidc_provider_url" {
  description = "The URL of the OIDC Provider for the EKS cluster"
  value       = local.oidc_issuer_url
}

output "oidc_provider_arn" {
  description = "The ARN of the OIDC Provider for the EKS cluster"
  value       = local.oidc_provider_arn
}

output "kubectl_config" {
  description = "kubectl config as generated by the aws eks get-kubeconfig-command"
  value       = local.cluster_exists || var.create_eks ? local.kubeconfig : ""
  sensitive   = true
}

