#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Get the bastion host IAM role ARN
BASTION_ROLE_ARN="arn:aws:iam::545009857703:role/production-eks-bastion-role"
echo -e "${YELLOW}Bastion host IAM role ARN: $BASTION_ROLE_ARN${NC}"

# Create a ConfigMap to allow the bastion host to access the EKS cluster
echo -e "${YELLOW}Creating ConfigMap to allow bastion host to access EKS cluster...${NC}"
cat > aws-auth-configmap.yaml << EOC
apiVersion: v1
kind: ConfigMap
metadata:
  name: aws-auth
  namespace: kube-system
data:
  mapRoles: |
    - rolearn: $BASTION_ROLE_ARN
      username: bastion-host
      groups:
        - system:masters
EOC

# Apply the ConfigMap
echo -e "${YELLOW}Applying ConfigMap...${NC}"
kubectl apply -f aws-auth-configmap.yaml

if [ \$? -eq 0 ]; then
    echo -e "${GREEN}ConfigMap applied successfully.${NC}"
else
    echo -e "${RED}Failed to apply ConfigMap.${NC}"
    exit 1
fi

# Verify the ConfigMap
echo -e "${YELLOW}Verifying ConfigMap...${NC}"
kubectl get configmap aws-auth -n kube-system -o yaml

echo -e "${GREEN}Done.${NC}"
