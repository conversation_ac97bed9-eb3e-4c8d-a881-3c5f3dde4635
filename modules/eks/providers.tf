terraform {
  required_version = ">= 1.0.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 4.0.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = ">= 2.0.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = ">= 2.0.0"
    }
    null = {
      source  = "hashicorp/null"
      version = ">= 3.0.0"
    }
    tls = {
      source  = "hashicorp/tls"
      version = ">= 3.0.0"
    }
  }
}

# Data sources have been moved to data.tf

locals {
  # Bastion configuration
  bastion_instance = {
    private_ip = var.bastion_private_ip
    public_ip  = var.bastion_public_ip
  }
  monitoring_bastion_cidr = "${var.bastion_private_ip}/32"
}
