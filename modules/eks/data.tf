# Data sources for the EKS module

# Lookup current AWS region
data "aws_region" "current" {}

# Lookup current AWS caller identity
data "aws_caller_identity" "current" {}

# Check if cluster exists before attempting to create it
# Use external data source to check if cluster exists without failing
data "external" "cluster_exists" {
  count = var.check_if_cluster_exists ? 1 : 0

  program = ["sh", "-c", <<-EOT
    cluster_exists=$(aws eks describe-cluster --name prod-architrave-eks 2>/dev/null || echo '{"exists": "false"}')
    if [ "$cluster_exists" != '{"exists": "false"}' ]; then
      echo '{"exists": "true"}'
    else
      echo '{"exists": "false"}'
    fi
  EOT
  ]
}

locals {
  # Remove these duplicate definitions:
  # current_ip = var.include_current_ip ? "${chomp(data.http.my_ip[0].response_body)}/32" : ""

  # allowed_ips = distinct(concat(
  #   var.public_access_cidrs != null ? var.public_access_cidrs : [],
  #   var.include_current_ip ? [local.current_ip] : []
  # ))
}

# Get cluster info for the existing cluster if it exists
data "aws_eks_cluster" "check_exists" {
  count = var.check_if_cluster_exists && local.cluster_exists ? 1 : 0
  name  = var.cluster_name
}

# Get auth token for the existing cluster if it exists
data "aws_eks_cluster_auth" "check_exists" {
  count = var.check_if_cluster_exists && local.cluster_exists ? 1 : 0
  name  = var.cluster_name
}

# Add data source for existing cluster if not creating one
data "aws_eks_cluster" "existing" {
  count = !var.create_eks && local.cluster_exists ? 1 : 0
  name  = var.cluster_name
}

# Add data source for existing cluster auth if not creating one
data "aws_eks_cluster_auth" "existing" {
  count = !var.create_eks && local.cluster_exists ? 1 : 0
  name  = var.cluster_name
}

# Add data source for new cluster auth if creating one
data "aws_eks_cluster_auth" "new" {
  count = var.create_eks ? 1 : 0
  name  = var.cluster_name
  # Remove the invalid module.eks dependency
}

# Data source for VPC
data "aws_vpc" "selected" {
  id = var.vpc_id
}

# Get current IP address for secure access
data "http" "my_ip" {
  count = var.include_current_ip ? 1 : 0
  url   = "https://ipv4.icanhazip.com"
}

# Get the latest Amazon Linux 2 AMI optimized for EKS
data "aws_ssm_parameter" "eks_ami" {
  name = "/aws/service/eks/optimized-ami/${var.kubernetes_version}/amazon-linux-2/recommended/image_id"
}

# Get the S3 prefix list ID for the region
data "aws_prefix_list" "s3" {
  name = "com.amazonaws.${data.aws_region.current.name}.s3"
}

# Get the certificate for the OIDC provider
data "tls_certificate" "existing_eks" {
  count = !var.create_eks && local.cluster_exists ? 1 : 0
  url   = data.aws_eks_cluster.existing[0].identity[0].oidc[0].issuer
}
