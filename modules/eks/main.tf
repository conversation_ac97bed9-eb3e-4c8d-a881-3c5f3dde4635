# EKS Module main configuration - Security Groups

resource "aws_security_group" "eks_cluster" {
  count       = var.create_security_groups ? 1 : 0
  name        = "${var.cluster_name}-cluster-sg"
  description = "Security group for EKS cluster control plane"
  vpc_id      = var.vpc_id

  # Allow HTTPS ingress from bastion host for kubectl access
  ingress {
    description = "HTTPS from bastion host"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  # Allow HTTPS ingress from bastion host public IP if specified
  dynamic "ingress" {
    for_each = var.bastion_public_ip != "0.0.0.0" && var.bastion_public_ip != "" ? [1] : []
    content {
      description = "HTTPS from bastion host public IP"
      from_port   = 443
      to_port     = 443
      protocol    = "tcp"
      cidr_blocks = ["${var.bastion_public_ip}/32"]
    }
  }

  # Allow HTTPS ingress from bastion host security group if specified
  dynamic "ingress" {
    for_each = var.bastion_security_group_id != "" ? [1] : []
    content {
      description     = "HTTPS from bastion host security group"
      from_port       = 443
      to_port         = 443
      protocol        = "tcp"
      security_groups = [var.bastion_security_group_id]
    }
  }

  # HTTPS ingress from nodes will be added later with aws_security_group_rule to avoid circular dependency

  tags = merge(
    var.tags,
    {
      "Name" = "${var.cluster_name}-cluster-sg"
    }
  )
}

resource "aws_security_group" "eks_node_group" {
  count       = var.create_security_groups ? 1 : 0
  name        = "${var.cluster_name}-node-sg"
  description = "Security group for EKS node groups"
  vpc_id      = var.vpc_id

  # Restrict egress traffic to only necessary destinations
  egress {
    description = "Allow outbound traffic within VPC"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [var.vpc_cidr]
  }

  # Allow HTTPS egress for ECR and other AWS services
  egress {
    description = "Allow HTTPS to AWS services"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    # Use prefix list for S3 VPC endpoint
    prefix_list_ids = [data.aws_prefix_list.s3.id]
  }

  # Allow NTP time synchronization
  egress {
    description = "Allow NTP time synchronization"
    from_port   = 123
    to_port     = 123
    protocol    = "udp"
    cidr_blocks = ["***************/32"]
  }

  # Allow DNS resolution
  egress {
    description = "Allow DNS resolution"
    from_port   = 53
    to_port     = 53
    protocol    = "udp"
    cidr_blocks = [var.vpc_cidr]
  }

  # More specific ingress rules for better security
  ingress {
    description = "Allow node to node communication"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    self        = true
  }

  # Allow specific ports from cluster API to nodes
  dynamic "ingress" {
    for_each = var.create_security_groups ? [1] : []
    content {
      description     = "Allow cluster API to communicate with kubelet"
      from_port       = 10250
      to_port         = 10250
      protocol        = "tcp"
      security_groups = [aws_security_group.eks_cluster[0].id]
    }
  }

  dynamic "ingress" {
    for_each = var.create_security_groups ? [1] : []
    content {
      description     = "Allow cluster API to communicate with nodes via HTTPS"
      from_port       = 443
      to_port         = 443
      protocol        = "tcp"
      security_groups = [aws_security_group.eks_cluster[0].id]
    }
  }

  # SSH access only from within VPC
  ingress {
    description = "SSH access from within VPC"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  # SSH access from bastion host public IP if specified
  dynamic "ingress" {
    for_each = var.bastion_public_ip != "0.0.0.0" && var.bastion_public_ip != "" ? [1] : []
    content {
      description = "SSH access from bastion host public IP"
      from_port   = 22
      to_port     = 22
      protocol    = "tcp"
      cidr_blocks = ["${var.bastion_public_ip}/32"]
    }
  }

  # SSH access from bastion host security group if specified
  dynamic "ingress" {
    for_each = var.bastion_security_group_id != "" ? [1] : []
    content {
      description     = "SSH access from bastion host security group"
      from_port       = 22
      to_port         = 22
      protocol        = "tcp"
      security_groups = [var.bastion_security_group_id]
    }
  }

  # Allow pod communication
  ingress {
    description = "Allow pod communication"
    from_port   = 1025
    to_port     = 65535
    protocol    = "tcp"
    self        = true
  }

  # Allow health checks
  ingress {
    description = "Allow health checks"
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  tags = merge(
    var.tags,
    {
      "Name" = "${var.cluster_name}-node-sg"
    }
  )
}

# Allow the cluster to communicate with worker nodes
resource "aws_security_group_rule" "cluster_egress_node_https" {
  count                    = var.create_security_groups ? 1 : 0
  description              = "Allow control plane to communicate with worker nodes"
  from_port                = 443
  protocol                 = "tcp"
  security_group_id        = aws_security_group.eks_cluster[0].id
  source_security_group_id = aws_security_group.eks_node_group[0].id
  to_port                  = 443
  type                     = "egress"
}

# Allow worker nodes to communicate with the cluster API Server
resource "aws_security_group_rule" "node_ingress_cluster_https" {
  count                    = var.create_security_groups ? 1 : 0
  description              = "Allow worker nodes to communicate with the cluster API Server"
  from_port                = 443
  protocol                 = "tcp"
  security_group_id        = aws_security_group.eks_cluster[0].id
  source_security_group_id = aws_security_group.eks_node_group[0].id
  to_port                  = 443
  type                     = "ingress"
}

# Allow restricted egress traffic from the cluster
resource "aws_security_group_rule" "cluster_egress_vpc" {
  count             = var.create_security_groups ? 1 : 0
  description       = "Allow control plane egress access within VPC"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  security_group_id = aws_security_group.eks_cluster[0].id
  cidr_blocks       = [var.vpc_cidr]
  type              = "egress"
}

# Allow HTTPS egress for AWS services
resource "aws_security_group_rule" "cluster_egress_aws_services" {
  count             = var.create_security_groups ? 1 : 0
  description       = "Allow control plane egress access to AWS services"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  security_group_id = aws_security_group.eks_cluster[0].id
  # Use specific AWS service CIDR blocks instead of 0.0.0.0/0
  # These are common AWS service CIDRs for eu-central-1
  prefix_list_ids = [data.aws_prefix_list.s3.id]
  type            = "egress"
}

# Create S3 VPC endpoint for secure access - conditional based on var.create_s3_vpc_endpoint
resource "aws_vpc_endpoint" "s3" {
  count             = var.create_s3_vpc_endpoint ? 1 : 0
  vpc_id            = var.vpc_id
  service_name      = "com.amazonaws.${data.aws_region.current.name}.s3"
  vpc_endpoint_type = "Gateway"
  route_table_ids   = var.route_table_ids

  tags = merge(var.tags, {
    Name = "${var.cluster_name}-s3-endpoint"
  })
}

# Using the S3 prefix list data source from data.tf

