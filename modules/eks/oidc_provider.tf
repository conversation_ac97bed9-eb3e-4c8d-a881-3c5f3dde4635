# OIDC Provider configuration for EKS
data "tls_certificate" "eks" {
  count = var.create_eks && length(aws_eks_cluster.this) > 0 ? 1 : 0
  url   = aws_eks_cluster.this[0].identity[0].oidc[0].issuer
}

# Create OIDC provider for EKS cluster
resource "aws_iam_openid_connect_provider" "eks_oidc" {
  count = var.create_eks && length(aws_eks_cluster.this) > 0 ? 1 : 0

  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = [data.tls_certificate.eks[0].certificates[0].sha1_fingerprint]
  url             = aws_eks_cluster.this[0].identity[0].oidc[0].issuer

  tags = merge(
    var.tags,
    {
      Name = "${var.cluster_name}-eks-oidc-provider"
    }
  )
}

# For existing clusters - data source is defined in data.tf

resource "aws_iam_openid_connect_provider" "existing_oidc" {
  count = var.create_eks ? 0 : (var.create_oidc_provider ? 1 : 0)

  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = [data.tls_certificate.existing_eks[0].certificates[0].sha1_fingerprint]
  url             = data.aws_eks_cluster.existing[0].identity[0].oidc[0].issuer

  tags = merge(
    var.tags,
    {
      Name = "${var.cluster_name}-eks-oidc-provider"
    }
  )
}

# For clusters that already exist when check_if_cluster_exists is true
data "tls_certificate" "check_exists_eks" {
  count = local.cluster_exists ? 1 : 0
  url   = data.aws_eks_cluster.check_exists[0].identity[0].oidc[0].issuer
}

resource "aws_iam_openid_connect_provider" "check_exists_oidc" {
  count = local.cluster_exists ? (var.create_oidc_provider ? 1 : 0) : 0

  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = [data.tls_certificate.check_exists_eks[0].certificates[0].sha1_fingerprint]
  url             = data.aws_eks_cluster.check_exists[0].identity[0].oidc[0].issuer

  tags = merge(
    var.tags,
    {
      Name = "${var.cluster_name}-eks-oidc-provider"
    }
  )
}

# Extract OIDC Provider URL without the protocol
locals {
  # Only replace if we have a valid URL
  oidc_provider_url_without_protocol = local.oidc_issuer_url != "" ? replace(local.oidc_issuer_url, "https://", "") : ""
}

# All outputs have been moved to outputs.tf to avoid duplication
