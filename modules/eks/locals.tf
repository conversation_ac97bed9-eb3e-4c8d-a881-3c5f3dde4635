locals {
  # Consolidated cluster existence check - use the variable to determine if cluster exists
  cluster_exists = var.check_if_cluster_exists ? try(tobool(data.external.cluster_exists[0].result.exists == "true"), false) : false

  # Get current IP or use provided ones - add null check for my_ip data source
  current_ip = var.include_current_ip ? "${try(chomp(data.http.my_ip[0].response_body), "")}/32" : ""

  allowed_ips = distinct(concat(
    var.public_access_cidrs != null ? var.public_access_cidrs : [],
    var.include_current_ip ? [local.current_ip] : []
  ))

  # Cluster endpoint and certificate for outputs
  cluster_endpoint       = var.create_eks ? try(aws_eks_cluster.this[0].endpoint, "") : try(data.aws_eks_cluster.existing[0].endpoint, "")
  cluster_ca_certificate = var.create_eks ? try(aws_eks_cluster.this[0].certificate_authority[0].data, "") : try(data.aws_eks_cluster.existing[0].certificate_authority[0].data, "")
  cluster_auth_token     = var.create_eks ? try(data.aws_eks_cluster_auth.new[0].token, "") : try(data.aws_eks_cluster_auth.existing[0].token, "")

  # OIDC provider URL and ARN
  oidc_issuer_url   = var.create_eks ? try(aws_eks_cluster.this[0].identity[0].oidc[0].issuer, "") : try(data.aws_eks_cluster.existing[0].identity[0].oidc[0].issuer, "")
  oidc_provider_arn = var.create_oidc_provider && var.create_eks ? try(aws_iam_openid_connect_provider.eks_oidc[0].arn, "") : ""

  # Kubeconfig configuration
  kubeconfig = templatefile("${path.module}/templates/kubeconfig.tpl", {
    cluster_name = var.cluster_name
    endpoint     = local.cluster_endpoint
    ca_data      = local.cluster_ca_certificate
  })
}
