# Cluster wait configuration
# This file contains resources that wait for the EKS cluster to be ready

# Wait for the EKS control plane to initialize
resource "time_sleep" "wait_for_cluster" {
  # If the cluster already exists, we don't need to wait for it to initialize
  count = var.create_eks && !local.cluster_exists ? 1 : 0

  depends_on = [aws_eks_cluster.this]
  # Increase duration to give the EKS control plane more time to initialize
  create_duration = "300s"
}

# Wait for nodes to join the cluster
resource "time_sleep" "wait_for_nodes" {
  # If the cluster already exists, we don't need to wait for nodes to join
  count = var.create_eks && !local.cluster_exists ? 1 : 0

  depends_on = [
    time_sleep.wait_for_cluster,
    aws_eks_node_group.main
  ]

  # Additional wait time after API is accessible to ensure nodes join
  create_duration = "60s"
}

# Wait for spot instances to join the cluster (if enabled)
resource "time_sleep" "wait_for_spot_nodes" {
  # If the cluster already exists or spot instances are disabled, we don't need to wait
  count = var.create_eks && !local.cluster_exists && var.enable_spot_instances ? 1 : 0

  depends_on = [
    time_sleep.wait_for_nodes,
    aws_eks_node_group.spot[0]
  ]

  # Additional wait time for spot instances
  create_duration = "30s"
}
