# IAM role for backend pods using IRSA (IAM Roles for Service Accounts)
resource "aws_iam_role" "backend_pod_role" {
  # Use explicit variable to control creation instead of depending on computed values
  count = var.create_backend_pod_role ? 1 : 0
  name  = "${var.cluster_name}-backend-pod-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRoleWithWebIdentity"
        Effect = "Allow"
        Principal = {
          Federated = var.external_oidc_provider_arn != "" ? var.external_oidc_provider_arn : (
            var.create_eks ? "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${local.oidc_provider_url_without_protocol}" :
            "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${local.oidc_provider_url_without_protocol}"
          )
        }
        Condition = {
          StringEquals = {
            "${local.oidc_provider_url_without_protocol}:sub" = "system:serviceaccount:default:backend-service-account"
          }
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      "Name" = "${var.cluster_name}-backend-pod-role"
    }
  )

  # Ensure this role is only created after the OIDC provider is available
  depends_on = [
    aws_iam_openid_connect_provider.eks_oidc,
    aws_iam_openid_connect_provider.existing_oidc,
    aws_iam_openid_connect_provider.check_exists_oidc
  ]
}

# Basic permissions for backend pods
resource "aws_iam_role_policy_attachment" "backend_pod_basic" {
  count      = var.create_backend_pod_role ? 1 : 0
  role       = aws_iam_role.backend_pod_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"
}

# Create S3 access policy for backend pods
resource "aws_iam_policy" "backend_s3_access" {
  count       = var.create_iam_policies ? 1 : 0
  name        = "${var.cluster_name}-backend-s3-access"
  description = "Policy for backend pods to access S3 buckets"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket",
          "s3:DeleteObject"
        ]
        Resource = [
          "arn:aws:s3:::${var.environment}-tenant-*",
          "arn:aws:s3:::${var.environment}-tenant-*/*",
          "arn:aws:s3:::${var.environment}-assets",
          "arn:aws:s3:::${var.environment}-assets/*"
        ]
      }
    ]
  })

  # Add lifecycle policy to handle existing resources
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_iam_role_policy_attachment" "backend_s3_access" {
  count      = var.create_backend_pod_role && var.create_iam_policies ? 1 : 0
  role       = aws_iam_role.backend_pod_role[0].name
  policy_arn = aws_iam_policy.backend_s3_access[0].arn
}

# Node role is defined in node_groups.tf

# Output the backend pod role ARN
output "backend_pod_role_arn" {
  description = "The ARN of the IAM role for backend pods"
  value       = var.create_backend_pod_role && length(aws_iam_role.backend_pod_role) > 0 ? aws_iam_role.backend_pod_role[0].arn : ""
}

# Output the node role ARN
output "node_role_arn" {
  description = "The ARN of the IAM role for EKS nodes"
  value       = var.create_iam_role && length(aws_iam_role.eks_node_role) > 0 ? aws_iam_role.eks_node_role[0].arn : ""
}
