# EKS Access Control Module
# Simple department-based user access management

locals {
  # Read the whitelist YAML file
  whitelist_file = "${path.root}/config/user-whitelist.yaml"
  
  # Parse whitelist data - file must exist
  whitelist_data = yamldecode(file(local.whitelist_file))
  
  # Extract all active users from enabled departments
  active_users = flatten([
    for dept_name, dept in local.whitelist_data.departments : [
      for user in (dept.users != null ? dept.users : []) : user
      if user.active == true && dept.enabled == true
    ]
  ])
  
  # Generate user CIDRs
  user_cidrs = [for user in local.active_users : "${user.ip}/32"]
  
  # Office VPN CIDRs
  office_cidrs = local.whitelist_data.office_vpn.enabled ? local.whitelist_data.office_vpn.ranges : []
  
  # Final authorized CIDRs list
  authorized_cidrs = concat(local.office_cidrs, local.user_cidrs)
}