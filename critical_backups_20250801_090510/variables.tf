variable "enable_data_lookups" {
  description = "Whether to enable lookups of existing resources (set to false during initial deployment)"
  type        = bool
  default     = false
}

variable "eks_cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
  default     = "prod-architrave-eks"
}

variable "domain_name" {
  description = "The root domain name for the infrastructure"
  type        = string
  default     = "architrave-assets.com" # Replace with your actual default domain
}

variable "domain" {
  description = "Alias for domain_name for backward compatibility"
  type        = string
  default     = "architrave-assets.com" # Same as domain_name
}

variable "domain_certificate_domain" {
  description = "The domain pattern for the ACM certificate lookup"
  type        = string
  default     = "" # This allows using the constructed value from domain_name when empty
}

variable "certificate_arn" {
  description = "ARN of the SSL certificate to use for HTTPS"
  type        = string
  default     = "" # Will be created or looked up if not provided
}

variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "aws_region" {
  description = "AWS region to deploy resources"
  type        = string
  default     = "us-west-2"
}

variable "vpc_cidr" {
  description = "The CIDR block for the VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnets" {
  description = "CIDR blocks for the public subnets"
  type        = list(string)
  default     = ["********/24", "********/24", "********/24"]
}

variable "private_subnets" {
  description = "CIDR blocks for the private subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24", "*********/24"]
}

variable "cluster_name" {
  description = "The name of the EKS cluster"
  type        = string
  default     = "prod-architrave-eks"
}

variable "tags" {
  description = "Map of tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "tenants" {
  description = "Map of tenant configurations"
  type = map(object({
    id                    = string
    namespace_prefix      = string
    display_name          = string
    description           = string
    rate_limit            = number
    security_threshold    = number
    db_role               = string
    db_password           = string
    rds_arn               = string
    efs_arn               = string
    backup_retention_days = number
    dr_team_role_arn      = string
    dr_test_schedule      = string
    backup_vault_arn      = string
    rds_instance_arn      = string
    ec2_instance_id       = string
    rds_instance_id       = string
    efs_id                = string
    ec2_cpu_threshold     = number
    rds_cpu_threshold     = number
    alb_arn_suffix        = string
    db_cpu_threshold      = number
    db_instance           = string
    waf_blocked_threshold = number
    compliance_rules      = list(string)
    contact = object({
      name  = string
      email = string
      phone = string
    })
    resource_quota = object({
      cpu      = string
      memory   = string
      storage  = string
      pods     = string
      services = string
    })
    network_policy = object({
      enabled            = bool
      allowed_namespaces = list(string)
    })
    database = object({
      enabled      = bool
      type         = string
      name         = string
      storage_size = string
      importFromS3 = bool
      s3Bucket     = string
      s3Key        = string
    })
    storage = object({
      enabled    = bool
      type       = string
      size       = string
      bucketName = string
      encryption = bool
    })
    monitoring = object({
      enabled    = bool
      grafana    = bool
      prometheus = bool
      alerting   = bool
      loki       = bool
      jaeger     = bool
    })
    ingress = object({
      enabled      = bool
      domain       = string
      tls          = bool
      istioGateway = string
      mtls         = bool
    })

    # API configuration
    api_config = optional(object({
      rate_limit_per_minute = optional(number, 1000)
      burst_limit          = optional(number, 2000)
      quota_per_day        = optional(number, 100000)
      enable_caching       = optional(bool, true)
      cache_ttl_seconds    = optional(number, 300)
      enable_compression   = optional(bool, true)
      cors_enabled         = optional(bool, true)
      cors_origins         = optional(list(string), ["*"])
    }), {})

    # Service mesh configuration
    service_mesh = optional(object({
      enable_injection = optional(bool, true)
      traffic_policy = optional(object({
        load_balancer = optional(string, "ROUND_ROBIN")
        connection_pool = optional(object({
          tcp = optional(object({
            max_connections = optional(number, 10)
            connect_timeout = optional(string, "10s")
          }), {})
          http = optional(object({
            http1_max_pending_requests = optional(number, 10)
            http2_max_requests        = optional(number, 100)
            max_requests_per_connection = optional(number, 2)
            max_retries               = optional(number, 3)
          }), {})
        }), {})
        outlier_detection = optional(object({
          consecutive_errors  = optional(number, 5)
          interval           = optional(string, "30s")
          base_ejection_time = optional(string, "30s")
          max_ejection_percent = optional(number, 50)
        }), {})
      }), {})
    }), {})
  }))
}

# Istio Service Mesh Variables
variable "istio_version" {
  description = "Istio version to install"
  type        = string
  default     = "1.20.1"
}

variable "mesh_id" {
  description = "Mesh ID for Istio"
  type        = string
  default     = "mesh1"
}

variable "cluster_network" {
  description = "Network name for the cluster"
  type        = string
  default     = "network1"
}

variable "trace_sampling" {
  description = "Trace sampling percentage"
  type        = number
  default     = 1.0
}

variable "enable_istio_ingress_gateway" {
  description = "Enable Istio ingress gateway"
  type        = bool
  default     = true
}

variable "enable_istio_egress_gateway" {
  description = "Enable Istio egress gateway"
  type        = bool
  default     = false
}

variable "enable_istio_mtls" {
  description = "Enable mutual TLS in Istio"
  type        = bool
  default     = true
}

variable "istio_mtls_mode" {
  description = "mTLS mode (STRICT, PERMISSIVE, DISABLE)"
  type        = string
  default     = "STRICT"
  validation {
    condition     = contains(["STRICT", "PERMISSIVE", "DISABLE"], var.istio_mtls_mode)
    error_message = "mTLS mode must be STRICT, PERMISSIVE, or DISABLE."
  }
}

variable "istio_pilot_resources" {
  description = "Resource requests and limits for Istio pilot"
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  default = {
    requests = {
      cpu    = "500m"
      memory = "2Gi"
    }
    limits = {
      cpu    = "1000m"
      memory = "4Gi"
    }
  }
}

variable "istio_circuit_breaker" {
  description = "Circuit breaker configuration for Istio"
  type = object({
    max_connections                = number
    connect_timeout               = string
    http1_max_pending_requests    = number
    http2_max_requests           = number
    max_requests_per_connection  = number
    max_retries                  = number
    consecutive_gateway_errors   = number
    interval                     = string
    base_ejection_time          = string
    max_ejection_percent        = number
    min_health_percent          = number
  })
  default = {
    max_connections                = 100
    connect_timeout               = "30s"
    http1_max_pending_requests    = 100
    http2_max_requests           = 100
    max_requests_per_connection  = 2
    max_retries                  = 3
    consecutive_gateway_errors   = 5
    interval                     = "30s"
    base_ejection_time          = "30s"
    max_ejection_percent        = 50
    min_health_percent          = 50
  }
}

# API Management Variables
variable "api_gateway_type" {
  description = "Type of API Gateway to use (aws, istio, kong, ambassador)"
  type        = string
  default     = "istio"
  validation {
    condition     = contains(["aws", "istio", "kong", "ambassador"], var.api_gateway_type)
    error_message = "API Gateway type must be one of: aws, istio, kong, ambassador."
  }
}

variable "api_rate_limiting" {
  description = "Global rate limiting configuration for APIs"
  type = object({
    enabled               = bool
    requests_per_minute   = number
    burst_limit          = number
    per_tenant_override  = bool
  })
  default = {
    enabled               = true
    requests_per_minute   = 1000
    burst_limit          = 2000
    per_tenant_override  = true
  }
}

variable "api_versions" {
  description = "API versioning configuration"
  type = object({
    strategy           = string # "path", "header", "query"
    default_version    = string
    supported_versions = list(string)
    deprecation_policy = object({
      notice_period_days = number
      sunset_period_days = number
    })
  })
  default = {
    strategy           = "path"
    default_version    = "v1"
    supported_versions = ["v1", "v2"]
    deprecation_policy = {
      notice_period_days = 90
      sunset_period_days = 180
    }
  }
}

variable "api_latency_threshold" {
  description = "API latency threshold for alarms (milliseconds)"
  type        = number
  default     = 1000
}

variable "api_error_rate_threshold" {
  description = "API error rate threshold for alarms"
  type        = number
  default     = 10
}

variable "kms_administrator_arns" {
  description = "List of IAM ARNs that can administer the KMS keys"
  type        = list(string)
  default     = []
}

variable "kms_user_arns" {
  description = "List of IAM ARNs that can use the KMS keys"
  type        = list(string)
  default     = []
}

variable "kms_key_deletion_window_in_days" {
  description = "Duration in days after which the key is deleted after destruction of the resource"
  type        = number
  default     = 7
}

variable "customer_id" {
  description = "The tenant ID for the deployment"
  type        = string
  default     = "default"
}

variable "is_ci_cd" {
  description = "Whether Terraform is running in a CI/CD environment"
  type        = bool
  default     = false
}

variable "private_zone" {
  description = "Whether to create a private Route53 zone"
  type        = bool
  default     = false
}

# Adding missing variables from terraform.tfvars
variable "administrator_arns" {
  description = "List of IAM ARNs with administrator permissions"
  type        = list(string)
  default     = []
}

variable "tenant_state" {
  description = "The current state of the tenant (e.g., onboard, active)"
  type        = string
  default     = "onboard"
}

variable "create_tenant_resources" {
  description = "Whether to create resources for the tenant"
  type        = bool
  default     = true
}

variable "rds_instance_class" {
  description = "The instance class for RDS instances"
  type        = string
  default     = "db.t3.small"
}

variable "rds_allocated_storage" {
  description = "The allocated storage for RDS instances in GB"
  type        = number
  default     = 20
}

variable "rds_engine_version" {
  description = "The engine version for RDS instances"
  type        = string
  default     = "13.7"
}

variable "rds_username" {
  description = "The master username for RDS instances"
  type        = string
  default     = "postgres"
  sensitive   = true
}

variable "rds_password" {
  description = "The master password for RDS instances"
  type        = string
  default     = null
  sensitive   = true
}

# This infrastructure does not support certain database services
variable "prohibited_services_check" {
  description = "Set to true to validate that prohibited services are not used"
  type        = bool
  default     = true

  validation {
    condition     = var.prohibited_services_check == true
    error_message = "Certain database services are not allowed in this infrastructure. Use approved alternatives only."
  }
}

variable "vpc_enable_dns_hostnames" {
  description = "Whether to enable DNS hostnames in the VPC"
  type        = bool
  default     = true
}

variable "vpc_enable_dns_support" {
  description = "Whether to enable DNS support in the VPC"
  type        = bool
  default     = true
}

variable "vpc_instance_tenancy" {
  description = "The tenancy of instances launched into the VPC"
  type        = string
  default     = "default"
}

variable "vpc_flow_logs_enabled" {
  description = "Whether to enable flow logs for the VPC"
  type        = bool
  default     = false
}

variable "vpc_flow_logs_traffic_type" {
  description = "The type of traffic to capture in flow logs"
  type        = string
  default     = "ALL"
}

variable "vpc_flow_logs_retention_in_days" {
  description = "The retention period for flow logs in days"
  type        = number
  default     = 90
}

# Newly identified variables from the warnings
variable "new_app_support_email" {
  description = "Email address for application support"
  type        = string
  default     = "<EMAIL>"
}

variable "max_size" {
  description = "Maximum size for Auto Scaling Groups"
  type        = number
  default     = 5
}

# Additional variables likely used in your terraform.tfvars
variable "min_size" {
  description = "Minimum size for Auto Scaling Groups"
  type        = number
  default     = 1
}

variable "desired_size" {
  description = "Desired size for Auto Scaling Groups"
  type        = number
  default     = 2
}

variable "instance_types" {
  description = "EC2 instance types to use for EKS node groups"
  type        = list(string)
  default     = ["t3.medium"]
}

variable "node_disk_size" {
  description = "Disk size for EKS worker nodes in GB"
  type        = number
  default     = 50
}

variable "kubernetes_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
  default     = "1.32"
}

# Using the existing eks_cluster_name variable defined at the top of the file

# Authentication and Access-related variables
variable "admin_users" {
  description = "List of admin users to be created"
  type        = list(string)
  default     = []
}

variable "developer_users" {
  description = "List of developer users to be created"
  type        = list(string)
  default     = []
}

variable "readonly_users" {
  description = "List of readonly users to be created"
  type        = list(string)
  default     = []
}

variable "cluster_admin_permissions" {
  description = "Whether to provide cluster admin permissions"
  type        = bool
  default     = true
}

# Additional email addresses often used in configuration
variable "app_support_email" {
  description = "Email address for application support"
  type        = string
  default     = "<EMAIL>"
}

variable "tenant_support_email" {
  description = "Email address for tenant support"
  type        = string
  default     = "<EMAIL>"
}

variable "ops_email" {
  description = "Email address for operations team"
  type        = string
  default     = "<EMAIL>"
}

variable "admin_email" {
  description = "Email address for admin notifications"
  type        = string
  default     = "<EMAIL>"
}

# Monitoring and logging settings
variable "enable_monitoring" {
  description = "Enable cloud monitoring"
  type        = bool
  default     = true
}

variable "log_retention_days" {
  description = "Number of days to retain logs"
  type        = number
  default     = 90
}

variable "alerts_enabled" {
  description = "Whether to enable alerting"
  type        = bool
  default     = true
}

variable "alert_emails" {
  description = "List of emails for alerts"
  type        = list(string)
  default     = ["<EMAIL>"]
}

# Security-related variables
variable "ip_whitelist" {
  description = "List of IPs allowed to access the infrastructure"
  type        = list(string)
  default     = []
}

variable "enable_network_policies" {
  description = "Whether to enable Kubernetes network policies"
  type        = bool
  default     = false
}

variable "enable_pod_security_policies" {
  description = "Whether to enable Kubernetes pod security policies"
  type        = bool
  default     = false
}

# EKS-specific configuration
variable "fargate_enabled" {
  description = "Whether to enable Fargate profiles for EKS"
  type        = bool
  default     = false
}

variable "fargate_namespaces" {
  description = "Kubernetes namespaces that should use Fargate"
  type        = list(string)
  default     = []
}

variable "endpoint_public_access" {
  description = "Whether to enable public access to the EKS API endpoint"
  type        = bool
  default     = true
}

variable "endpoint_private_access" {
  description = "Whether to enable private access to the EKS API endpoint"
  type        = bool
  default     = true
}

variable "public_access_cidrs" {
  description = "CIDR blocks that can access the EKS API endpoint publicly"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

# Database configuration
variable "db_backup_retention_period" {
  description = "How many days to keep database backups"
  type        = number
  default     = 7
}

variable "db_deletion_protection" {
  description = "Whether to enable deletion protection for databases"
  type        = bool
  default     = false # Changed to false to allow easier resource management during development
}

variable "db_multi_az" {
  description = "Whether to enable Multi-AZ deployment for databases"
  type        = bool
  default     = true
}

# Newly identified variables from the latest warnings
variable "new_image_tag" {
  description = "Docker image tag for new deployments"
  type        = string
  default     = "latest"
}

variable "new_app_customer_email" {
  description = "Customer email for the new application"
  type        = string
  default     = "<EMAIL>"
}

# Additional variables likely used in your terraform.tfvars

# Email-related variables
variable "app_customer_support_email" {
  description = "Email for customer support within the app"
  type        = string
  default     = "<EMAIL>"
}

variable "app_sysadmin_email" {
  description = "Email for system administration"
  type        = string
  default     = "<EMAIL>"
}

variable "app_sysadmin_name" {
  description = "Name of the system administrator"
  type        = string
  default     = "System Administrator"
}

# Container and image related variables
variable "image_tag" {
  description = "Docker image tag for current deployments"
  type        = string
  default     = "latest"
}

variable "image_repository" {
  description = "Docker image repository"
  type        = string
  default     = "example"
}

variable "image_pull_policy" {
  description = "Kubernetes image pull policy"
  type        = string
  default     = "IfNotPresent"
}

# Authentication and security
variable "api_key" {
  description = "API key for external services"
  type        = string
  default     = ""
  sensitive   = true
}

variable "secret_key" {
  description = "Secret key for encryption"
  type        = string
  default     = ""
  sensitive   = true
}

variable "oauth_client_id" {
  description = "OAuth client ID"
  type        = string
  default     = ""
}

variable "oauth_client_secret" {
  description = "OAuth client secret"
  type        = string
  default     = ""
  sensitive   = true
}

# Infrastructure variables
variable "enable_bastion" {
  description = "Whether to deploy a bastion host"
  type        = bool
  default     = true
}

variable "skip_k8s_connection" {
  description = "Whether to skip Kubernetes connection for Helm charts"
  type        = bool
  default     = true
}

variable "skip_tenant_resources" {
  description = "Skip creating tenant custom resources (useful for initial CRD deployment)"
  type        = bool
  default     = false
}

variable "bastion_instance_type" {
  description = "Instance type for bastion host"
  type        = string
  default     = "t3.micro"
}

variable "bastion_ami" {
  description = "AMI ID for the bastion host"
  type        = string
  default     = "ami-0c55b159cbfafe1f0" # Amazon Linux 2 LTS AMI
}

variable "bastion_allowed_cidrs" {
  description = "List of CIDR blocks allowed to SSH into the bastion host"
  type        = list(string)
  default     = [] # Empty list by default for security, specific CIDRs should be provided in terraform.tfvars

  validation {
    condition = alltrue([
      for cidr in var.bastion_allowed_cidrs :
      !can(regex("^0\\.0\\.0\\.0/0$", cidr))
    ])
    error_message = "Public internet access (0.0.0.0/0) is not allowed for SSH ingress rules."
  }
}

variable "bastion_public_ip" {
  description = "Public IP of the bastion host"
  type        = string
  default     = ""
}

variable "monitoring_bastion_cidr" {
  description = "CIDR block for monitoring bastion access"
  type        = string
  default     = ""
}

# Application-specific variables
variable "app_customer" {
  description = "Name of the customer application"
  type        = string
  default     = "customer-app"
}

variable "app_tenant" {
  description = "Name of the tenant application"
  type        = string
  default     = "tenant-app"
}

variable "app_name" {
  description = "Name of the application"
  type        = string
  default     = "app"
}

# Certificate ARN variable is already defined above

variable "app_version" {
  description = "Current version of the application"
  type        = string
  default     = "1.0.0"
}

# Directory paths
variable "customer_details_directory" {
  description = "Directory path for customer details"
  type        = string
  default     = "./customer-details"
}

variable "tenant_details_directory" {
  description = "Directory path for tenant details"
  type        = string
  default     = "./tenant-details"
}

# Schedule-related variables
variable "backup_schedule" {
  description = "Backup schedule in cron format"
  type        = string
  default     = "0 2 * * *"
}

variable "maintenance_window" {
  description = "Maintenance window schedule"
  type        = string
  default     = "Mon:03:00-Mon:04:00"
}

# Other configuration variables
variable "environment_variables" {
  description = "Environment variables to set in containers"
  type        = map(string)
  default     = {}
}

variable "terraform_state_bucket" {
  description = "S3 bucket for Terraform state"
  type        = string
  default     = ""
}

variable "terraform_state_key" {
  description = "S3 key for Terraform state"
  type        = string
  default     = "terraform.tfstate"
}

# New variables from latest warnings
# Note: skip_eks_connectivity_check and check_if_cluster_exists are defined in import_existing_resources.tf

variable "new_app_sysadmin_name" {
  description = "Name of the new application's system administrator"
  type        = string
  default     = "Admin User"
}

# Additional likely variables from the remaining 15
variable "enable_cloudwatch_container_insights" {
  description = "Enable CloudWatch Container Insights for EKS"
  type        = bool
  default     = true
}

variable "backup_retention_days" {
  description = "Number of days to retain backups"
  type        = number
  default     = 7
}

variable "enable_node_auto_scaling" {
  description = "Enable automatic scaling for EKS node groups"
  type        = bool
  default     = true
}

variable "enable_ssm_access" {
  description = "Enable SSM session manager access for instances"
  type        = bool
  default     = true
}

variable "allowed_regions" {
  description = "List of allowed AWS regions for resource deployment"
  type        = list(string)
  default     = ["eu-central-1"]
}

variable "cost_center_tag" {
  description = "Cost center tag for resources"
  type        = string
  default     = "Platform"
}

variable "enable_cost_allocation_tags" {
  description = "Enable cost allocation tags for all resources"
  type        = bool
  default     = true
}

variable "enable_vpc_flow_logs" {
  description = "Enable VPC flow logs"
  type        = bool
  default     = true
}

variable "enable_vpc_endpoints" {
  description = "Enable VPC endpoints for AWS services"
  type        = bool
  default     = true
}

variable "enable_cloudwatch_encryption" {
  description = "Enable encryption for CloudWatch logs"
  type        = bool
  default     = true
}

variable "enable_container_insights" {
  description = "Enable Container Insights for EKS"
  type        = bool
  default     = true
}

variable "enable_bastion_ssh" {
  description = "Enable SSH access to bastion host"
  type        = bool
  default     = false
}

# Variable is_ci_cd is already defined above

# Variable skip_k8s_connection is already defined above

variable "flow_logs_retention" {
  description = "Retention period for VPC flow logs in days"
  type        = number
  default     = 365
}

variable "enable_guardduty" {
  description = "Enable AWS GuardDuty monitoring"
  type        = bool
  default     = true
}

variable "enable_security_hub" {
  description = "Enable AWS Security Hub integration"
  type        = bool
  default     = true
}

variable "enable_config_recorder" {
  description = "Enable AWS Config recorder"
  type        = bool
  default     = true
}

variable "enable_cloudtrail" {
  description = "Enable AWS CloudTrail logging"
  type        = bool
  default     = true
}

variable "enable_inspector" {
  description = "Enable Amazon Inspector for security scanning"
  type        = bool
  default     = true
}

variable "s3_access_logs_bucket" {
  description = "Name of S3 bucket for access logs"
  type        = string
  default     = ""
}

variable "enable_cross_region_backup" {
  description = "Enable cross-region backups"
  type        = bool
  default     = false
}

variable "disaster_recovery_region" {
  description = "AWS region for disaster recovery"
  type        = string
  default     = "eu-west-1"
}

# Application-specific variables
variable "enable_feature_flag_system" {
  description = "Enable feature flag management system"
  type        = bool
  default     = true
}

# System admin email
variable "new_app_sysadmin_email" {
  description = "Email address for system administrator notifications"
  type        = string
  default     = ""
}

# Feature flags
variable "customer_management_enabled" {
  description = "Flag to enable/disable customer management features"
  type        = bool
  default     = false
}

variable "allowed_cidr_blocks" {
  description = "List of CIDR blocks allowed for internal access"
  type        = list(string)
  default     = []
}

variable "new_app_customer" {
  description = "Name of the customer for the new application"
  type        = string
  default     = "default-customer"
}

# Variable enable_vpc_endpoints is already defined above

variable "allowed_ports" {
  description = "List of allowed ingress ports"
  type        = list(number)
  default     = [80, 443]
}

variable "internal_subnets" {
  description = "CIDR blocks for internal subnets"
  type        = list(string)
  default     = []
}

variable "enable_encryption" {
  description = "Enable encryption for resources"
  type        = bool
  default     = true
}

variable "kms_key_arn" {
  description = "ARN of KMS key for encryption"
  type        = string
  default     = ""
}

variable "audit_logging" {
  description = "Enable audit logging"
  type        = bool
  default     = true
}

variable "compliance_tags" {
  description = "Tags for compliance requirements"
  type        = map(string)
  default     = {}
}

variable "gdpr_compliant" {
  description = "Enable GDPR compliance features"
  type        = bool
  default     = false
}

variable "enable_auto_patching" {
  description = "Enable automatic OS patching"
  type        = bool
  default     = true
}

variable "feature_flags" {
  description = "Map of feature flags"
  type        = map(bool)
  default     = {}
}

# Variable enable_bastion_ssh is already defined above

variable "new_app_customer_support_email" {
  description = "Support email for the new customer application"
  type        = string
  default     = ""
}

variable "new_fqdn" {
  description = "Fully qualified domain name for the new deployment"
  type        = string
  default     = ""
}

variable "enable_access_logs" {
  description = "Whether to enable access logs for the ALB"
  type        = bool
  default     = false
}

# These variables are already defined elsewhere in the file
# Removed duplicate variable declarations for:
# - enable_monitoring
# - enable_container_insights
# - enable_cloudwatch_encryption
# - enable_vpc_endpoints

variable "new_customer_id" {
  description = "Customer ID for the new tenant"
  type        = string
  default     = ""
}

# Variable skip_k8s_connection is already defined above

variable "new_customer_details_directory" {
  description = "Directory path containing new customer details"
  type        = string
  default     = ""
}

variable "user_arns" {
  description = "List of IAM role ARNs for users"
  type        = list(string)
  default     = []
}

variable "allowed_ssh_cidr_blocks" {
  description = "CIDR blocks allowed to SSH to bastion host"
  type        = list(string)
  default     = []
}

variable "skip_rds_connectivity_check" {
  description = "Set to true to skip RDS connectivity checks"
  type        = bool
  default     = false
}

variable "skip_kubernetes_resources" {
  description = "Set to true to skip Kubernetes resource creation"
  type        = bool
  default     = false
}

variable "eks_cluster_exists" {
  description = "Whether the EKS cluster already exists"
  type        = bool
  default     = false # Set to false since we've deleted the EKS cluster
}

variable "create_eks" {
  description = "Whether to create the EKS cluster"
  type        = bool
  default     = true
}

variable "create_eks_log_group" {
  description = "Whether to create the EKS CloudWatch log group"
  type        = bool
  default     = true
}

variable "create_security_groups" {
  description = "Whether to create security groups for EKS"
  type        = bool
  default     = true
}

variable "create_node_groups" {
  description = "Whether to create EKS node groups"
  type        = bool
  default     = true
}

variable "create_iam_policies" {
  description = "Whether to create IAM policies for EKS"
  type        = bool
  default     = true
}

# Variable check_if_cluster_exists is defined in import_existing_resources.tf

variable "vpc_id" {
  description = "ID of the VPC where resources will be created"
  type        = string
  default     = ""
}

# This variable is already defined in provider.tf
# variable "availability_zones" {
#   description = "List of availability zones to use"
#   type        = list(string)
#   default     = ["eu-central-1a", "eu-central-1b", "eu-central-1c"]
# }

# Variable bastion_public_ip is already defined above

# Variables enable_container_insights and enable_cloudwatch_encryption are already defined above

variable "pipeline_name" {
  description = "Name of the deployment pipeline"
  type        = string
  default     = "infra-deployment"
}

variable "pipeline_artifact_bucket" {
  description = "S3 bucket name for pipeline artifacts"
  type        = string
  default     = ""
}

variable "pipeline_source_branch" {
  description = "Source branch for the pipeline"
  type        = string
  default     = "main"
}

variable "pipeline_source_repo" {
  description = "Source repository for the pipeline"
  type        = string
  default     = ""
}

variable "pipeline_notification_email" {
  description = "Email address for pipeline notifications"
  type        = string
  default     = ""
}

variable "pipeline_approval_required" {
  description = "Whether manual approval is required for pipeline deployments"
  type        = bool
  default     = true
}

variable "pipeline_timeout_minutes" {
  description = "Timeout in minutes for pipeline stages"
  type        = number
  default     = 60
}

variable "pipeline_role_arn" {
  description = "IAM role ARN for the pipeline execution"
  type        = string
  default     = ""
}

variable "pipeline_enabled" {
  description = "Whether the pipeline is enabled"
  type        = bool
  default     = true
}

# Service Mesh Configuration
variable "enable_service_mesh" {
  description = "Enable Service Mesh (Istio) deployment"
  type        = bool
  default     = false
}

# Autoscaling Configuration
variable "enable_auto_scaling" {
  description = "Enable advanced autoscaling features"
  type        = bool
  default     = false
}

# Monitoring Configuration
variable "enable_advanced_monitoring" {
  description = "Enable advanced monitoring features"
  type        = bool
  default     = false
}

# Tenant Management Configuration
variable "enable_tenant_management" {
  description = "Enable tenant management features"
  type        = bool
  default     = false
}

# Additional variables for bastion configuration
variable "region" {
  description = "The AWS region to deploy to (alias for aws_region for backward compatibility)"
  type        = string
  default     = "us-east-1"
}

variable "bastion_key_name" {
  description = "Name of the SSH key pair to use for bastion host"
  type        = string
  default     = "bastion-key"
}

variable "ssh_private_key_path" {
  description = "Path to the SSH private key file for connecting to the bastion"
  type        = string
  default     = "~/.ssh/id_rsa"
}

variable "bastion_subnet_id" {
  description = "Subnet ID where the bastion host should be launched"
  type        = string
  default     = ""
}

variable "bastion_security_group_id" {
  description = "Security group ID for the bastion host"
  type        = string
  default     = ""
}

variable "bastion_user" {
  description = "Username for SSH access to the bastion host"
  type        = string
  default     = "ec2-user"
}

variable "bastion_ssh_port" {
  description = "SSH port for the bastion host"
  type        = number
  default     = 22
}

# Add this to your existing variables.tf file

variable "grafana_admin_password" {
  description = "Password for Grafana admin user"
  type        = string
  default     = "StrongPassword123!" # Change this in production
  sensitive   = true
}

variable "allowed_ssh_cidrs" {
  description = "List of CIDR blocks allowed to SSH to the bastion host"
  type        = list(string)
  default     = []
}

variable "skip_problematic_modules" {
  description = "Set to true to skip problematic modules"
  type        = bool
  default     = false
}

# This variable is already defined at line 1022
# variable "enable_service_mesh" {
#   description = "Enable Service Mesh (Istio) deployment"
#   type        = bool
#   default     = false
# }

variable "enable_kiali" {
  description = "Enable Kiali for service mesh visualization"
  type        = bool
  default     = true
}

variable "kiali_version" {
  description = "Version of Kiali to deploy"
  type        = string
  default     = "1.57.1"
}

variable "enable_jaeger" {
  description = "Enable Jaeger for distributed tracing"
  type        = bool
  default     = true
}

variable "jaeger_version" {
  description = "Version of Jaeger to deploy"
  type        = string
  default     = "0.47.0"
}

variable "istio_gateway_service_type" {
  description = "Service type for Istio gateway (LoadBalancer, NodePort, ClusterIP)"
  type        = string
  default     = "LoadBalancer"
}

# Advanced Monitoring variables
variable "prometheus_version" {
  description = "Version of Prometheus to deploy"
  type        = string
  default     = "15.10.1"
}

variable "grafana_version" {
  description = "Version of Grafana to deploy"
  type        = string
  default     = "6.32.1"
}

variable "loki_version" {
  description = "Version of Loki to deploy"
  type        = string
  default     = "2.9.1"
}

variable "prometheus_retention_days" {
  description = "Number of days to retain Prometheus metrics"
  type        = number
  default     = 15
}

variable "prometheus_storage_size" {
  description = "Storage size for Prometheus"
  type        = string
  default     = "50Gi"
}

variable "loki_persistence_enabled" {
  description = "Enable persistence for Loki"
  type        = bool
  default     = true
}

variable "loki_persistence_size" {
  description = "Storage size for Loki"
  type        = string
  default     = "20Gi"
}

variable "loki_retention_period" {
  description = "Retention period for Loki logs"
  type        = string
  default     = "168h"
}

variable "enable_cloudwatch_integration" {
  description = "Enable CloudWatch integration for monitoring"
  type        = bool
  default     = true
}

variable "cloudwatch_exporter_version" {
  description = "Version of CloudWatch exporter to deploy"
  type        = string
  default     = "0.15.0"
}

variable "enable_tenant_specific_dashboards" {
  description = "Enable tenant-specific dashboards"
  type        = bool
  default     = true
}

# Advanced Autoscaling variables
variable "enable_keda" {
  description = "Enable KEDA for event-driven autoscaling"
  type        = bool
  default     = true
}

variable "keda_version" {
  description = "Version of KEDA to deploy"
  type        = string
  default     = "2.9.4"
}

variable "enable_vpa" {
  description = "Enable Vertical Pod Autoscaler"
  type        = bool
  default     = true
}

variable "vpa_version" {
  description = "Version of Vertical Pod Autoscaler to deploy"
  type        = string
  default     = "1.4.0"
}

variable "enable_goldilocks" {
  description = "Enable Goldilocks for VPA recommendations"
  type        = bool
  default     = true
}

variable "goldilocks_version" {
  description = "Version of Goldilocks to deploy"
  type        = string
  default     = "4.4.2"
}

variable "enable_karpenter" {
  description = "Enable Karpenter for advanced node provisioning"
  type        = bool
  default     = false
}

variable "karpenter_version" {
  description = "Version of Karpenter to deploy"
  type        = string
  default     = "0.16.3"
}

# Network Load Balancers variables
variable "enable_network_load_balancers" {
  description = "Enable Network Load Balancers"
  type        = bool
  default     = true
}

variable "nlb_internal" {
  description = "Whether NLB should be internal"
  type        = bool
  default     = true
}

variable "nlb_cross_zone_enabled" {
  description = "Enable cross-zone load balancing for NLB"
  type        = bool
  default     = true
}

# Tenant Management System variables
variable "enable_tenant_operator" {
  description = "Enable Tenant Operator for tenant lifecycle management"
  type        = bool
  default     = true
}

variable "tenant_namespace_prefix" {
  description = "Prefix for tenant namespaces"
  type        = string
  default     = "tenant-"
}

variable "tenant_operator_resources" {
  description = "Resource limits and requests for tenant operator"
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  default = {
    requests = {
      cpu    = "50m"
      memory = "64Mi"
    }
    limits = {
      cpu    = "100m"
      memory = "128Mi"
    }
  }
}

# Customer Dashboards variables
variable "enable_customer_dashboards" {
  description = "Enable Customer Dashboards"
  type        = bool
  default     = true
}

variable "customer_dashboard_namespace" {
  description = "Namespace for customer dashboards"
  type        = string
  default     = "customer-dashboards"
}

# Advanced Security Features variables
# These variables are already defined elsewhere in the file
# variable "enable_network_policies" {
#   description = "Enable Network Policies for pod-to-pod communication"
#   type        = bool
#   default     = true
# }
#
# variable "enable_pod_security_policies" {
#   description = "Enable Pod Security Policies"
#   type        = bool
#   default     = true
# }

variable "create_iam_role" {
  description = "Whether to create an IAM role for the EKS cluster"
  type        = bool
  default     = true
}

variable "iam_role_name_prefix" {
  description = "Prefix for IAM role names"
  type        = string
  default     = ""
}

variable "owner" {
  description = "Owner of the resource"
  type        = string
  default     = "DevOps"
}

variable "project" {
  description = "Project name"
  type        = string
  default     = "Infrastructure"
}

variable "cost_center" {
  description = "Cost center for billing"
  type        = string
  default     = "IT"
}

variable "data_classification" {
  description = "Data classification level"
  type        = string
  default     = "Internal"
}

variable "compliance_requirements" {
  description = "Compliance requirements"
  type        = string
  default     = "Standard"
}

variable "db_name" {
  description = "Name of the database"
  type        = string
}

variable "db_host" {
  description = "Database host address"
  type        = string
}

variable "db_username" {
  description = "Database admin username"
  type        = string
}

variable "db_password" {
  description = "Database admin password"
  type        = string
  sensitive   = true
}

variable "eks_cluster_endpoint" {
  description = "The endpoint for the EKS cluster"
  type        = string
}

variable "eks_cluster_ca_certificate" {
  description = "The CA certificate for the EKS cluster"
  type        = string
  default     = ""
}

variable "name" {
  description = "Name prefix for resources (for tenant module)"
  type        = string
  default     = "tenant-management"
}

variable "operator_namespace" {
  description = "Kubernetes namespace for the tenant operator (for tenant module)"
  type        = string
  default     = "tenant-system"
}

variable "create_operator_namespace" {
  description = "Whether to create the operator namespace (for tenant module)"
  type        = bool
  default     = true
}

variable "operator_image_repository" {
  description = "Repository for the tenant operator image (for tenant module)"
  type        = string
  default     = "architrave/tenant-operator"
}

variable "operator_image_tag" {
  description = "Tag for the tenant operator image (for tenant module)"
  type        = string
  default     = "latest"
}

variable "operator_replicas" {
  description = "Number of replicas for the tenant operator (for tenant module)"
  type        = number
  default     = 1
}

variable "operator_log_level" {
  description = "Log level for the tenant operator (for tenant module)"
  type        = string
  default     = "info"
}

variable "operator_resources" {
  description = "Resource requests and limits for the tenant operator (for tenant module)"
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  default = {
    requests = {
      cpu    = "100m"
      memory = "128Mi"
    }
    limits = {
      cpu    = "200m"
      memory = "256Mi"
    }
  }
}

variable "operator_role_arn" {
  description = "ARN of the IAM role for the tenant operator (for tenant module)"
  type        = string
  default     = ""
}

variable "tenant_kms_key_arns" {
  description = "Map of tenant IDs to KMS key ARNs for tenant-specific encryption"
  type        = map(string)
  default     = {}
}

variable "database_secret_arn" {
  description = "ARN of the Secrets Manager secret containing database credentials"
  type        = string
  default     = ""
}

variable "s3_bucket_name" {
  description = "Name of the S3 bucket for tenant storage"
  type        = string
  default     = ""
}

variable "default_resource_quota_cpu" {
  description = "Default CPU resource quota for tenants"
  type        = string
  default     = "1000m"
}

variable "default_resource_quota_memory" {
  description = "Default memory resource quota for tenants"
  type        = string
  default     = "2Gi"
}

variable "default_resource_quota_storage" {
  description = "Default storage resource quota for tenants"
  type        = string
  default     = "10Gi"
}

variable "default_resource_quota_pods" {
  description = "Default pod count resource quota for tenants"
  type        = string
  default     = "10"
}

variable "prometheus_namespace" {
  description = "Namespace where Prometheus is deployed"
  type        = string
  default     = "monitoring"
}

variable "grafana_namespace" {
  description = "Namespace where Grafana is deployed"
  type        = string
  default     = "monitoring"
}

variable "account_id" {
  description = "AWS account ID"
  type        = string
  default     = null # Will be populated automatically from aws_caller_identity
}
