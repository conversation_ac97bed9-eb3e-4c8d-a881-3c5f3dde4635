apiVersion: karpenter.sh/v1alpha5
kind: Provisioner
metadata:
  name: default
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  # Limit to specific instance types
  requirements:
    - key: "kubernetes.io/arch"
      operator: In
      values: ["amd64"]
    - key: "kubernetes.io/os"
      operator: In
      values: ["linux"]
    - key: "karpenter.sh/capacity-type"
      operator: In
      values: ["spot", "on-demand"]
    - key: "node.kubernetes.io/instance-type"
      operator: In
      values:
        - "t3a.small"
        - "t3.small"
        - "t3a.medium"
        - "t3.medium"
        - "t3a.large"
        - "t3.large"
        - "m5.large"
        - "m5a.large"
        - "c5.large"
        - "c5a.large"
  # Configure labels for nodes
  labels:
    provisioning-group: default
  # Configure taints for nodes
  taints: []
  # Configure resource limits
  limits:
    resources:
      cpu: 100
      memory: 200Gi
  # Configure node expiry
  ttlSecondsAfterEmpty: 30
  ttlSecondsUntilExpired: 2592000 # 30 days
  # Configure consolidation to optimize node usage
  consolidation:
    enabled: true
  # Configure provider-specific settings
  providerRef:
    name: default

---
apiVersion: karpenter.sh/v1alpha5
kind: Provisioner
metadata:
  name: tenant-provisioner
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  # Limit to specific instance types
  requirements:
    - key: "kubernetes.io/arch"
      operator: In
      values: ["amd64"]
    - key: "kubernetes.io/os"
      operator: In
      values: ["linux"]
    - key: "karpenter.sh/capacity-type"
      operator: In
      values: ["spot", "on-demand"]
    - key: "node.kubernetes.io/instance-type"
      operator: In
      values:
        - "t3a.small"
        - "t3.small"
        - "t3a.medium"
        - "t3.medium"
  # Limit the provisioner to tenant namespaces
  labels:
    workload-type: tenant
  # Taints ensure that only tenant workloads will be scheduled on these nodes
  taints:
    - key: workload-type
      value: tenant
      effect: NoSchedule
  # Configure resource limits
  limits:
    resources:
      cpu: 20
      memory: 40Gi
  # Configure node expiry
  ttlSecondsAfterEmpty: 30
  ttlSecondsUntilExpired: 2592000 # 30 days
  # Configure consolidation to optimize node usage
  consolidation:
    enabled: true
  # Configure provider-specific settings
  providerRef:
    name: default

---
apiVersion: karpenter.k8s.aws/v1alpha1
kind: AWSNodeTemplate
metadata:
  name: default
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  subnetSelector:
    karpenter.sh/discovery: "true"
  securityGroupSelector:
    karpenter.sh/discovery: "true"
  tags:
    karpenter.sh/discovery: "true"
  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 50Gi
        volumeType: gp3
        iops: 3000
        deleteOnTermination: true
        encrypted: true
