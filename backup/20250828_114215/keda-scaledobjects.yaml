apiVersion: v1
items:
- apiVersion: keda.sh/v1alpha1
  kind: ScaledObject
  metadata:
    annotations:
      meta.helm.sh/release-name: tenant-keda-test
      meta.helm.sh/release-namespace: tenant-keda-test
    creationTimestamp: "2025-08-27T14:56:20Z"
    finalizers:
    - finalizer.keda.sh
    generation: 1
    labels:
      app: database-worker
      app.kubernetes.io/instance: tenant-keda-test
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: architrave-tenantchart2
      app.kubernetes.io/version: 2.0.49
      component: keda-scaler
      helm.sh/chart: architrave-tenantchart2-1.0.0
      owner: architrave
      scaledobject.keda.sh/name: keda-test-database-scaler
      tenant: keda-test
    name: keda-test-database-scaler
    namespace: tenant-keda-test
    resourceVersion: "24979931"
    uid: af0b5a6d-46ba-45cb-b5e4-2ed2c702e30b
  spec:
    cooldownPeriod: 180
    maxReplicaCount: 5
    minReplicaCount: 1
    pollingInterval: 15
    scaleTargetRef:
      name: keda-test-database-worker
    triggers:
    - metadata:
        connectionStringFromEnv: DATABASE_URL
        queryValue: SELECT COUNT(*) FROM information_schema.processlist WHERE db=''
          AND user=''
        targetQueryValue: "10"
      type: mysql
    - metadata:
        metricName: mysql_active_connections
        query: mysql_global_status_threads_connected{instance="production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com:3306"}
        serverAddress: http://prometheus.monitoring.svc.cluster.local:9090
        threshold: "15"
      type: prometheus
  status:
    authenticationsTypes: ""
    conditions:
    - message: failed to ensure HPA is correctly created for ScaledObject
      reason: ScaledObjectCheckFailed
      status: "False"
      type: Ready
    - message: ScaledObject check failed
      reason: UnknownState
      status: Unknown
      type: Active
    - message: No fallbacks are active on this scaled object
      reason: NoFallbackFound
      status: "False"
      type: Fallback
    - status: Unknown
      type: Paused
    originalReplicaCount: 1
    scaleTargetGVKR:
      group: apps
      kind: Deployment
      resource: deployments
      version: v1
    scaleTargetKind: apps/v1.Deployment
    triggersTypes: mysql,prometheus
- apiVersion: keda.sh/v1alpha1
  kind: ScaledObject
  metadata:
    annotations:
      meta.helm.sh/release-name: tenant-keda-test
      meta.helm.sh/release-namespace: tenant-keda-test
    creationTimestamp: "2025-08-27T14:56:20Z"
    finalizers:
    - finalizer.keda.sh
    generation: 2
    labels:
      app: default-queue
      app.kubernetes.io/instance: tenant-keda-test
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: architrave-tenantchart2
      app.kubernetes.io/version: 2.0.49
      component: keda-scaler
      helm.sh/chart: architrave-tenantchart2-1.0.0
      owner: architrave
      scaledobject.keda.sh/name: keda-test-default-queue-scaler
      tenant: keda-test
    name: keda-test-default-queue-scaler
    namespace: tenant-keda-test
    resourceVersion: "24979922"
    uid: 94af06f9-b798-4f19-941c-4ede8b374c1b
  spec:
    advanced:
      horizontalPodAutoscalerConfig:
        behavior:
          scaleDown:
            stabilizationWindowSeconds: 60
          scaleUp:
            stabilizationWindowSeconds: 0
      scalingModifiers: {}
    cooldownPeriod: 120
    maxReplicaCount: 20
    minReplicaCount: 0
    pollingInterval: 10
    scaleTargetRef:
      name: keda-test-default-queue
    triggers:
    - authenticationRef:
        name: keda-test-rabbitmq-auth
      metadata:
        host: keda-test-rabbitmq:5672
        protocol: amqp
        queueLength: "3"
        queueName: keda-test_default_queue
        username: guest
        vhostName: /
      type: rabbitmq
  status:
    authenticationsTypes: keda-test-rabbitmq-auth
    conditions:
    - message: failed to ensure HPA is correctly created for ScaledObject
      reason: ScaledObjectCheckFailed
      status: "False"
      type: Ready
    - message: ScaledObject check failed
      reason: UnknownState
      status: Unknown
      type: Active
    - message: No fallbacks are active on this scaled object
      reason: NoFallbackFound
      status: "False"
      type: Fallback
    - status: Unknown
      type: Paused
    originalReplicaCount: 1
    scaleTargetGVKR:
      group: apps
      kind: Deployment
      resource: deployments
      version: v1
    scaleTargetKind: apps/v1.Deployment
    triggersTypes: rabbitmq
- apiVersion: keda.sh/v1alpha1
  kind: ScaledObject
  metadata:
    annotations:
      meta.helm.sh/release-name: tenant-keda-test
      meta.helm.sh/release-namespace: tenant-keda-test
    creationTimestamp: "2025-08-27T14:56:20Z"
    finalizers:
    - finalizer.keda.sh
    generation: 2
    labels:
      app: folder-queue
      app.kubernetes.io/instance: tenant-keda-test
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: architrave-tenantchart2
      app.kubernetes.io/version: 2.0.49
      component: keda-scaler
      helm.sh/chart: architrave-tenantchart2-1.0.0
      owner: architrave
      scaledobject.keda.sh/name: keda-test-folder-queue-scaler
      tenant: keda-test
    name: keda-test-folder-queue-scaler
    namespace: tenant-keda-test
    resourceVersion: "24979962"
    uid: 81f95b48-f426-4954-8ba3-c57670b52d76
  spec:
    advanced:
      horizontalPodAutoscalerConfig:
        behavior:
          scaleDown:
            stabilizationWindowSeconds: 60
          scaleUp:
            stabilizationWindowSeconds: 0
      scalingModifiers: {}
    cooldownPeriod: 120
    maxReplicaCount: 15
    minReplicaCount: 0
    pollingInterval: 10
    scaleTargetRef:
      name: keda-test-folder-queue
    triggers:
    - authenticationRef:
        name: keda-test-rabbitmq-auth
      metadata:
        host: keda-test-rabbitmq:5672
        protocol: amqp
        queueLength: "2"
        queueName: keda-test_folder_queue
        username: guest
        vhostName: /
      type: rabbitmq
  status:
    authenticationsTypes: keda-test-rabbitmq-auth
    conditions:
    - message: failed to ensure HPA is correctly created for ScaledObject
      reason: ScaledObjectCheckFailed
      status: "False"
      type: Ready
    - message: ScaledObject check failed
      reason: UnknownState
      status: Unknown
      type: Active
    - message: No fallbacks are active on this scaled object
      reason: NoFallbackFound
      status: "False"
      type: Fallback
    - status: Unknown
      type: Paused
    originalReplicaCount: 1
    scaleTargetGVKR:
      group: apps
      kind: Deployment
      resource: deployments
      version: v1
    scaleTargetKind: apps/v1.Deployment
    triggersTypes: rabbitmq
- apiVersion: keda.sh/v1alpha1
  kind: ScaledObject
  metadata:
    annotations:
      meta.helm.sh/release-name: tenant-keda-test
      meta.helm.sh/release-namespace: tenant-keda-test
    creationTimestamp: "2025-08-27T14:56:20Z"
    finalizers:
    - finalizer.keda.sh
    generation: 2
    labels:
      app: notification-queue
      app.kubernetes.io/instance: tenant-keda-test
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: architrave-tenantchart2
      app.kubernetes.io/version: 2.0.49
      component: keda-scaler
      helm.sh/chart: architrave-tenantchart2-1.0.0
      owner: architrave
      scaledobject.keda.sh/name: keda-test-notification-queue-scaler
      tenant: keda-test
    name: keda-test-notification-queue-scaler
    namespace: tenant-keda-test
    resourceVersion: "24979924"
    uid: ee5a62fb-e364-4a95-a4c1-70c0277d7f41
  spec:
    advanced:
      horizontalPodAutoscalerConfig:
        behavior:
          scaleDown:
            stabilizationWindowSeconds: 30
          scaleUp:
            stabilizationWindowSeconds: 0
      scalingModifiers: {}
    cooldownPeriod: 60
    maxReplicaCount: 10
    minReplicaCount: 0
    pollingInterval: 5
    scaleTargetRef:
      name: keda-test-notification-queue
    triggers:
    - authenticationRef:
        name: keda-test-rabbitmq-auth
      metadata:
        host: keda-test-rabbitmq:5672
        protocol: amqp
        queueLength: "1"
        queueName: keda-test_notification_queue
        username: guest
        vhostName: /
      type: rabbitmq
  status:
    authenticationsTypes: keda-test-rabbitmq-auth
    conditions:
    - message: failed to ensure HPA is correctly created for ScaledObject
      reason: ScaledObjectCheckFailed
      status: "False"
      type: Ready
    - message: ScaledObject check failed
      reason: UnknownState
      status: Unknown
      type: Active
    - message: No fallbacks are active on this scaled object
      reason: NoFallbackFound
      status: "False"
      type: Fallback
    - status: Unknown
      type: Paused
    originalReplicaCount: 1
    scaleTargetGVKR:
      group: apps
      kind: Deployment
      resource: deployments
      version: v1
    scaleTargetKind: apps/v1.Deployment
    triggersTypes: rabbitmq
- apiVersion: keda.sh/v1alpha1
  kind: ScaledObject
  metadata:
    annotations:
      meta.helm.sh/release-name: tenant-keda-test
      meta.helm.sh/release-namespace: tenant-keda-test
    creationTimestamp: "2025-08-27T14:56:20Z"
    finalizers:
    - finalizer.keda.sh
    generation: 1
    labels:
      app: onboarding
      app.kubernetes.io/instance: tenant-keda-test
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: architrave-tenantchart2
      app.kubernetes.io/version: 2.0.49
      component: keda-scaler
      helm.sh/chart: architrave-tenantchart2-1.0.0
      owner: architrave
      scaledobject.keda.sh/name: keda-test-onboarding-scaler
      tenant: keda-test
    name: keda-test-onboarding-scaler
    namespace: tenant-keda-test
    resourceVersion: "24979921"
    uid: c8f1669a-6b40-4ee6-ab33-78b3c3a2c31a
  spec:
    cooldownPeriod: 60
    maxReplicaCount: 10
    minReplicaCount: 0
    pollingInterval: 5
    scaleTargetRef:
      name: keda-test-onboarding-processor
    triggers:
    - authenticationRef:
        name: keda-test-rabbitmq-auth
      metadata:
        host: keda-test-rabbitmq:5672
        protocol: amqp
        queueLength: "1"
        queueName: keda-test_onboarding_queue
        username: guest
        vhostName: /
      type: rabbitmq
    - metadata:
        podSelector: app=keda-test-onboarding,phase=Pending
        value: "1"
      type: kubernetes-workload
  status:
    authenticationsTypes: keda-test-rabbitmq-auth
    conditions:
    - message: failed to ensure HPA is correctly created for ScaledObject
      reason: ScaledObjectCheckFailed
      status: "False"
      type: Ready
    - message: ScaledObject check failed
      reason: UnknownState
      status: Unknown
      type: Active
    - message: No fallbacks are active on this scaled object
      reason: NoFallbackFound
      status: "False"
      type: Fallback
    - status: Unknown
      type: Paused
    originalReplicaCount: 0
    scaleTargetGVKR:
      group: apps
      kind: Deployment
      resource: deployments
      version: v1
    scaleTargetKind: apps/v1.Deployment
    triggersTypes: rabbitmq,kubernetes-workload
- apiVersion: keda.sh/v1alpha1
  kind: ScaledObject
  metadata:
    annotations:
      meta.helm.sh/release-name: tenant-keda-test
      meta.helm.sh/release-namespace: tenant-keda-test
    creationTimestamp: "2025-08-27T14:56:20Z"
    finalizers:
    - finalizer.keda.sh
    generation: 1
    labels:
      app: webapp
      app.kubernetes.io/instance: tenant-keda-test
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: architrave-tenantchart2
      app.kubernetes.io/version: 2.0.49
      component: keda-scaler
      helm.sh/chart: architrave-tenantchart2-1.0.0
      owner: architrave
      scaledobject.keda.sh/name: keda-test-webapp-scaler
      tenant: keda-test
    name: keda-test-webapp-scaler
    namespace: tenant-keda-test
    resourceVersion: "25004367"
    uid: 1b157a73-f769-4393-9f52-c18b83ecdfb4
  spec:
    cooldownPeriod: 180
    maxReplicaCount: 8
    minReplicaCount: 1
    pollingInterval: 20
    scaleTargetRef:
      name: keda-test-webapp
    triggers:
    - metadata:
        value: "60"
      metricType: Utilization
      type: cpu
    - metadata:
        value: "70"
      metricType: Utilization
      type: memory
    - metadata:
        metricName: webapp_request_rate
        query: rate(http_requests_total{job="keda-test-webapp"}[1m])
        serverAddress: http://prometheus.monitoring.svc.cluster.local:9090
        threshold: "100"
      type: prometheus
  status:
    authenticationsTypes: ""
    conditions:
    - message: ScaledObject is defined correctly and is ready for scaling
      reason: ScaledObjectReady
      status: "True"
      type: Ready
    - message: Scaling is not performed because triggers are not active
      reason: ScalerNotActive
      status: "False"
      type: Active
    - message: No fallbacks are active on this scaled object
      reason: NoFallbackFound
      status: "False"
      type: Fallback
    - status: Unknown
      type: Paused
    externalMetricNames:
    - s2-prometheus
    hpaName: keda-hpa-keda-test-webapp-scaler
    originalReplicaCount: 1
    resourceMetricNames:
    - cpu
    - memory
    scaleTargetGVKR:
      group: apps
      kind: Deployment
      resource: deployments
      version: v1
    scaleTargetKind: apps/v1.Deployment
    triggersTypes: cpu,memory,prometheus
kind: List
metadata:
  resourceVersion: ""
